# eCommFlex .gitignore
# Comprehensive ignore file for <PERSON><PERSON> + React + TypeScript + Inertia.js project
# Updated: January 2025

# Laravel Framework
/.phpunit.cache
/bootstrap/ssr
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
/auth.json

# Environment Files
.env
.env.backup
.env.production
.env.local
.env.testing
.env.dusk.local

# Database Files
/database/*.sqlite
/database/*.sqlite-journal
/database/*.db
/database/*.db-journal

# Node.js & NPM
/node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.npm
.yarn-integrity
.pnpm-debug.log

# Build Outputs & Compiled Assets
/public/build
/public/hot
/public/storage
/public/mix-manifest.json
/public/js/app.js
/public/css/app.css
dist/
build/

# TypeScript
*.tsbuildinfo
.tscache/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
/storage/logs/*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE & Editor Files
/.fleet
/.idea
/.nova
/.vscode
/.zed
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Temporary Files
*.tmp
*.temp
*.bak
*.backup
*.orig
*.rej
*~

# PHP Specific
*.cache
.phpunit.result.cache
.php_cs.cache
.php-cs-fixer.cache

# Laravel Specific
/storage/framework/cache/data/*
!/storage/framework/cache/data/.gitignore
/storage/framework/sessions/*
!/storage/framework/sessions/.gitignore
/storage/framework/views/*
!/storage/framework/views/.gitignore
/storage/framework/testing/*
!/storage/framework/testing/.gitignore
/storage/app/*
!/storage/app/public/
!/storage/app/private/
!/storage/app/.gitignore
/storage/logs/*
!/storage/logs/.gitignore
/bootstrap/cache/*
!/bootstrap/cache/.gitignore

# Inertia.js
/resources/js/ziggy.js
/resources/js/ziggy.d.ts

# Testing & Test Results
/coverage
/.phpunit.cache
phpunit.xml
.phpunit.result.cache
/test-results
/playwright-report
/tests/results
/tests/reports

# Documentation Build
/docs/_build/
/docs/.doctrees/

# eCommFlex Specific
/docs/development-progress-checklist.md
git-message.txt

# Playwright & E2E Testing
/test-results/
/playwright-report/
/playwright/.cache/

# Security & Sensitive Files
*.pem
*.key
*.crt
*.p12
*.pfx
auth.json

# Backup Files
*.sql
*.dump
*.backup

# Local Development
.local
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# Compressed files
*.zip
*.rar
*.7z
*.gz
*.tar
*.tar.gz
*.bz2
*.xz

# Development & Debug Files
*.debug
*.trace
*.prof
*.heapsnapshot

# Package Manager Lock Files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Docker Development
docker-compose.override.yml
.dockerignore.local

# Local Configuration
.env.local.*
config.local.json
settings.local.json

# Temporary Scripts & Tools
/scripts/temp/
/scripts/debug/
/tools/temp/
