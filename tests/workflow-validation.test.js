#!/usr/bin/env node

/**
 * GitHub Actions Workflow Validation Test Suite
 * Node.js-based test for validating workflow configuration
 */

import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const projectRoot = process.cwd();
let testsRun = 0;
let testsPassed = 0;
let testsFailed = 0;

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

// Test data
const requiredWorkflowFiles = [
  '.github/workflows/deploy-staging.yml',
  '.github/workflows/deploy-production.yml',
  '.github/workflows/ci.yml',
  '.github/workflows/tests.yml',
];

const requiredCompositeActions = [
  '.github/actions/validate-deployment-secrets/action.yml',
  '.github/actions/setup-deployment-ssh/action.yml',
  '.github/actions/deploy-with-backup/action.yml',
];

const stagingSecrets = [
  'STAGING_SSH_PRIVATE_KEY',
  'STAGING_HOST',
  'STAGING_USER',
  'STAGING_PATH',
];

const productionSecrets = [
  'PRODUCTION_SSH_PRIVATE_KEY',
  'PRODUCTION_HOST',
  'PRODUCTION_USER',
  'PRODUCTION_PATH',
];

// Test helper functions
function test(name, testFn) {
  testsRun++;
  try {
    testFn();
    console.log(`${colors.green}✅ ${name}${colors.reset}`);
    testsPassed++;
  } catch (error) {
    console.log(`${colors.red}❌ ${name}${colors.reset}`);
    console.log(`   ${error.message}`);
    testsFailed++;
  }
}

function expect(actual, message = '') {
  return {
    toBe(expected) {
      if (actual !== expected) {
        throw new Error(`${message} - Expected: ${expected}, Actual: ${actual}`);
      }
    },
    toContain(expected) {
      if (!actual.includes(expected)) {
        throw new Error(`${message} - Expected to contain: ${expected}`);
      }
    },
    toBeGreaterThanOrEqual(expected) {
      if (actual < expected) {
        throw new Error(`${message} - Expected ${actual} to be >= ${expected}`);
      }
    },
    toBeTruthy() {
      if (!actual) {
        throw new Error(`${message} - Expected truthy value, got: ${actual}`);
      }
    },
    toHaveProperty(property) {
      if (!actual.hasOwnProperty(property)) {
        throw new Error(`${message} - Expected object to have property: ${property}`);
      }
    }
  };
}

// Test functions
function testWorkflowFilesExist() {
  for (const file of requiredWorkflowFiles) {
    const filePath = path.join(projectRoot, file);
    expect(fs.existsSync(filePath), `Required workflow file missing: ${file}`).toBeTruthy();
  }
}

function testWorkflowYamlSyntax() {
  for (const file of requiredWorkflowFiles) {
    const filePath = path.join(projectRoot, file);
    
    if (!fs.existsSync(filePath)) {
      continue;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    try {
      yaml.load(content);
    } catch (error) {
      throw new Error(`YAML syntax invalid for ${file}: ${error.message}`);
    }
  }
}

function testCompositeActionsExist() {
  for (const action of requiredCompositeActions) {
    const filePath = path.join(projectRoot, action);
    expect(fs.existsSync(filePath), `Required composite action missing: ${action}`).toBeTruthy();
  }
}

function testCompositeActionsYamlSyntax() {
  for (const action of requiredCompositeActions) {
    const filePath = path.join(projectRoot, action);
    
    if (!fs.existsSync(filePath)) {
      continue;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    try {
      yaml.load(content);
    } catch (error) {
      throw new Error(`YAML syntax invalid for ${action}: ${error.message}`);
    }
  }
}

function testSecretValidationJobs() {
  const deploymentWorkflows = [
    '.github/workflows/deploy-staging.yml',
    '.github/workflows/deploy-production.yml',
  ];

  for (const workflow of deploymentWorkflows) {
    const filePath = path.join(projectRoot, workflow);
    
    if (!fs.existsSync(filePath)) {
      continue;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    expect(content, `Missing secret validation job in: ${workflow}`).toContain('validate-secrets:');
  }
}

function testSecretDocumentation() {
  // Test staging secrets
  const stagingPath = path.join(projectRoot, '.github/workflows/deploy-staging.yml');
  if (fs.existsSync(stagingPath)) {
    const content = fs.readFileSync(stagingPath, 'utf8');
    for (const secret of stagingSecrets) {
      expect(content, `Staging secret not documented: ${secret}`).toContain(secret);
    }
  }

  // Test production secrets
  const productionPath = path.join(projectRoot, '.github/workflows/deploy-production.yml');
  if (fs.existsSync(productionPath)) {
    const content = fs.readFileSync(productionPath, 'utf8');
    for (const secret of productionSecrets) {
      expect(content, `Production secret not documented: ${secret}`).toContain(secret);
    }
  }
}

function testJobDependencies() {
  const deploymentWorkflows = [
    '.github/workflows/deploy-staging.yml',
    '.github/workflows/deploy-production.yml',
  ];

  for (const workflow of deploymentWorkflows) {
    const filePath = path.join(projectRoot, workflow);
    
    if (!fs.existsSync(filePath)) {
      continue;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const needsCount = (content.match(/needs:/g) || []).length;
    
    expect(needsCount, `Insufficient job dependencies in ${workflow}`).toBeGreaterThanOrEqual(3);
  }
}

function testEnvironmentConfiguration() {
  // Test staging environment
  const stagingPath = path.join(projectRoot, '.github/workflows/deploy-staging.yml');
  if (fs.existsSync(stagingPath)) {
    const content = fs.readFileSync(stagingPath, 'utf8');
    expect(content, 'Staging workflow missing environment configuration').toContain('environment:');
    expect(content, 'Staging workflow missing environment name').toContain('name: staging');
  }

  // Test production environment
  const productionPath = path.join(projectRoot, '.github/workflows/deploy-production.yml');
  if (fs.existsSync(productionPath)) {
    const content = fs.readFileSync(productionPath, 'utf8');
    expect(content, 'Production workflow missing environment configuration').toContain('environment:');
    expect(content, 'Production workflow missing environment name').toContain('name: production');
  }
}

function testValidationScriptExists() {
  const scriptPath = path.join(projectRoot, 'scripts/validate-workflows.sh');
  expect(fs.existsSync(scriptPath), 'Validation script missing').toBeTruthy();
  
  // Check if file is executable (on Unix-like systems)
  if (process.platform !== 'win32') {
    const stats = fs.statSync(scriptPath);
    const isExecutable = !!(stats.mode & parseInt('111', 8));
    expect(isExecutable, 'Validation script is not executable').toBeTruthy();
  }
}

function testWorkflowStructureIntegrity() {
  const deploymentWorkflows = [
    '.github/workflows/deploy-staging.yml',
    '.github/workflows/deploy-production.yml',
  ];

  for (const workflow of deploymentWorkflows) {
    const filePath = path.join(projectRoot, workflow);
    
    if (!fs.existsSync(filePath)) {
      continue;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const parsed = yaml.load(content);
    
    // Check basic structure
    expect(parsed, `Missing 'name' in ${workflow}`).toHaveProperty('name');
    expect(parsed, `Missing 'on' in ${workflow}`).toHaveProperty('on');
    expect(parsed, `Missing 'jobs' in ${workflow}`).toHaveProperty('jobs');
    
    // Check for validate-secrets job
    expect(parsed.jobs, `Missing 'validate-secrets' job in ${workflow}`).toHaveProperty('validate-secrets');
  }
}

// Main test execution
function runTests() {
  console.log(`${colors.blue}🚀 Starting GitHub Actions Workflow Test Suite${colors.reset}`);
  console.log('============================================================');

  test('Workflow files should exist', testWorkflowFilesExist);
  test('Workflow YAML syntax should be valid', testWorkflowYamlSyntax);
  test('Composite actions should exist', testCompositeActionsExist);
  test('Composite actions YAML syntax should be valid', testCompositeActionsYamlSyntax);
  test('Workflows should have secret validation jobs', testSecretValidationJobs);
  test('Secrets should be documented', testSecretDocumentation);
  test('Workflows should have proper job dependencies', testJobDependencies);
  test('Workflows should have environment configuration', testEnvironmentConfiguration);
  test('Validation script should exist and be executable', testValidationScriptExists);
  test('Workflow structure integrity should be maintained', testWorkflowStructureIntegrity);

  // Print summary
  console.log('');
  console.log('============================================================');
  console.log('📊 Test Summary:');
  console.log(`  Tests Run: ${testsRun}`);
  console.log(`  Tests Passed: ${testsPassed}`);
  console.log(`  Tests Failed: ${testsFailed}`);
  console.log('============================================================');

  if (testsFailed === 0) {
    console.log(`${colors.green}✅ All workflow validation tests passed!${colors.reset}`);
    process.exit(0);
  } else {
    console.log(`${colors.red}❌ ${testsFailed} workflow validation tests failed.${colors.reset}`);
    process.exit(1);
  }
}

// Run tests if script is executed directly
runTests();

export { runTests };
