[opcache]
; OPcache configuration for eCommFlex

; Enable OPcache
opcache.enable = 1
opcache.enable_cli = 0

; Memory settings
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000

; Performance settings
opcache.revalidate_freq = 0
opcache.validate_timestamps = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1

; File cache settings
opcache.file_cache = /tmp/opcache
opcache.file_cache_only = 0
opcache.file_cache_consistency_checks = 1

; Optimization settings
opcache.optimization_level = 0x7FFFBFFF
opcache.inherited_hack = 1
opcache.dups_fix = 0
opcache.blacklist_filename = /var/www/opcache-blacklist.txt

; JIT settings (PHP 8.0+)
opcache.jit_buffer_size = 100M
opcache.jit = tracing

; Preloading (for production)
; opcache.preload = /var/www/preload.php
; opcache.preload_user = www-data

; Error handling
opcache.log_verbosity_level = 1
opcache.preferred_memory_model = mmap

; Security
opcache.restrict_api = ""

; Huge pages support (if available)
opcache.huge_code_pages = 1
