# eCommFlex Troubleshooting Guide

## CI/CD Pipeline Issues

### GitHub Actions Workflow Failures

#### Test Failures
**Symptoms:**
- Tests failing in CI but passing locally
- Inconsistent test results
- Database-related test failures

**Diagnosis:**
```bash
# Check workflow logs in GitHub Actions
# Look for specific error messages in test output
```

**Solutions:**
1. **Environment Differences:**
   ```bash
   # Ensure CI environment matches local
   # Check .env.ci configuration
   # Verify database seeding and migrations
   ```

2. **Database Issues:**
   ```bash
   # Clear and refresh test database
   php artisan migrate:fresh --seed --env=testing
   
   # Check for race conditions in tests
   # Use database transactions in tests
   ```

3. **Dependency Issues:**
   ```bash
   # Clear and reinstall dependencies
   composer install --no-scripts
   npm ci
   ```

#### Build Failures
**Symptoms:**
- Asset compilation failures
- Composer dependency conflicts
- Node.js build errors

**Solutions:**
1. **Frontend Build Issues:**
   ```bash
   # Clear node_modules and package-lock.json
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

2. **PHP Dependency Conflicts:**
   ```bash
   # Update composer dependencies
   composer update
   
   # Check for version conflicts
   composer why-not php 8.4
   ```

#### Deployment Failures
**Symptoms:**
- SSH connection failures
- Permission denied errors
- Service restart failures

**Solutions:**
1. **SSH Issues:**
   ```bash
   # Verify SSH key in GitHub secrets
   # Test SSH connection manually
   ssh <EMAIL>
   ```

2. **Permission Issues:**
   ```bash
   # Fix file permissions on server
   sudo chown -R deploy:www-data /var/www/ecommflex-*
   sudo chmod -R 775 storage bootstrap/cache
   ```

3. **Service Issues:**
   ```bash
   # Restart services manually
   sudo systemctl restart nginx
   sudo systemctl restart php8.4-fpm
   sudo supervisorctl restart all
   ```

### Security Scan Failures

#### Dependency Vulnerabilities
**Symptoms:**
- npm audit failures
- Composer security warnings
- Container scan alerts

**Solutions:**
1. **Update Dependencies:**
   ```bash
   # Update npm packages
   npm audit fix
   npm update
   
   # Update Composer packages
   composer update
   ```

2. **Manual Fixes:**
   ```bash
   # Check specific vulnerabilities
   npm audit --audit-level=moderate
   
   # Update specific packages
   npm install package@latest
   ```

#### Secrets Detection
**Symptoms:**
- TruffleHog alerts
- Exposed API keys
- Hardcoded credentials

**Solutions:**
1. **Remove Secrets:**
   ```bash
   # Remove from git history
   git filter-branch --force --index-filter \
     'git rm --cached --ignore-unmatch path/to/file' \
     --prune-empty --tag-name-filter cat -- --all
   ```

2. **Use Environment Variables:**
   ```php
   // Instead of hardcoded values
   $apiKey = env('API_KEY');
   ```

## Application Issues

### Performance Problems

#### Slow Response Times
**Symptoms:**
- Page load times > 3 seconds
- API responses > 500ms
- Database query timeouts

**Diagnosis:**
```bash
# Check application logs
tail -f storage/logs/laravel.log

# Monitor database queries
php artisan telescope:install
```

**Solutions:**
1. **Database Optimization:**
   ```sql
   -- Add missing indexes
   CREATE INDEX idx_users_email ON users(email);
   
   -- Analyze slow queries
   SHOW PROCESSLIST;
   SHOW FULL PROCESSLIST;
   ```

2. **Cache Implementation:**
   ```php
   // Cache expensive operations
   $result = Cache::remember('expensive_operation', 3600, function () {
       return expensiveOperation();
   });
   ```

3. **Query Optimization:**
   ```php
   // Use eager loading
   $users = User::with('orders')->get();
   
   // Avoid N+1 queries
   $orders = Order::with('user', 'items')->get();
   ```

#### High Memory Usage
**Symptoms:**
- Memory limit exceeded errors
- Server crashes
- Slow performance

**Solutions:**
1. **Increase Memory Limit:**
   ```ini
   ; In php.ini
   memory_limit = 512M
   ```

2. **Optimize Code:**
   ```php
   // Use chunking for large datasets
   User::chunk(1000, function ($users) {
       foreach ($users as $user) {
           // Process user
       }
   });
   ```

3. **Enable OPcache:**
   ```ini
   ; In opcache.ini
   opcache.enable=1
   opcache.memory_consumption=256
   opcache.max_accelerated_files=10000
   ```

### Database Issues

#### Connection Failures
**Symptoms:**
- "Connection refused" errors
- "Too many connections" errors
- Timeout errors

**Solutions:**
1. **Check MySQL Status:**
   ```bash
   sudo systemctl status mysql
   sudo systemctl restart mysql
   ```

2. **Connection Pool Configuration:**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=ecommflex
   DB_USERNAME=ecommflex
   DB_PASSWORD=your-password
   ```

3. **Increase Connection Limits:**
   ```sql
   -- In MySQL configuration
   SET GLOBAL max_connections = 500;
   ```

#### Migration Failures
**Symptoms:**
- Migration rollback errors
- Schema conflicts
- Foreign key constraint failures

**Solutions:**
1. **Check Migration Status:**
   ```bash
   php artisan migrate:status
   ```

2. **Rollback and Retry:**
   ```bash
   php artisan migrate:rollback
   php artisan migrate
   ```

3. **Fix Schema Issues:**
   ```php
   // In migration file
   Schema::table('table_name', function (Blueprint $table) {
       $table->dropForeign(['foreign_key']);
       $table->foreign('foreign_key')->references('id')->on('other_table');
   });
   ```

### Multi-Tenant Issues

#### Tenant Isolation Problems
**Symptoms:**
- Cross-tenant data leakage
- Wrong database connections
- Cache pollution between tenants

**Diagnosis:**
```bash
# Check current tenant context
php artisan tinker
>>> app('current_tenant')

# Verify database connections
>>> DB::getDefaultConnection()
```

**Solutions:**
1. **Fix Tenant Context:**
   ```php
   // Ensure tenant is set correctly
   app()->instance('current_tenant', $tenant);
   
   // Switch database connection
   Config::set('database.default', "tenant_{$tenant}");
   DB::purge("tenant_{$tenant}");
   DB::reconnect("tenant_{$tenant}");
   ```

2. **Cache Isolation:**
   ```php
   // Use tenant-specific cache keys
   $key = "tenant_{$tenant}_cache_key";
   Cache::put($key, $value);
   ```

3. **Session Isolation:**
   ```php
   // Configure tenant-specific session prefix
   Config::set('session.cookie', "tenant_{$tenant}_session");
   ```

#### Subdomain Routing Issues
**Symptoms:**
- 404 errors on tenant subdomains
- Incorrect tenant resolution
- DNS resolution failures

**Solutions:**
1. **Check DNS Configuration:**
   ```bash
   # Test DNS resolution
   nslookup tenant1.ecommflex.com
   dig tenant1.ecommflex.com
   ```

2. **Nginx Configuration:**
   ```nginx
   # Wildcard subdomain configuration
   server {
       listen 80;
       server_name *.ecommflex.com;
       # ... rest of configuration
   }
   ```

3. **Laravel Route Configuration:**
   ```php
   // In routes/web.php
   Route::domain('{tenant}.ecommflex.com')->group(function () {
       // Tenant-specific routes
   });
   ```

### Queue Issues

#### Jobs Not Processing
**Symptoms:**
- Jobs stuck in queue
- Failed jobs accumulating
- Queue workers not running

**Solutions:**
1. **Check Queue Workers:**
   ```bash
   # Check supervisor status
   sudo supervisorctl status
   
   # Restart queue workers
   sudo supervisorctl restart ecommflex-queue:*
   ```

2. **Clear Failed Jobs:**
   ```bash
   # View failed jobs
   php artisan queue:failed
   
   # Retry failed jobs
   php artisan queue:retry all
   
   # Clear failed jobs
   php artisan queue:flush
   ```

3. **Monitor Queue:**
   ```bash
   # Real-time queue monitoring
   php artisan queue:monitor
   
   # Check queue size
   php artisan queue:work --once
   ```

#### Memory Leaks in Workers
**Symptoms:**
- Workers consuming excessive memory
- Workers dying unexpectedly
- Performance degradation over time

**Solutions:**
1. **Configure Worker Limits:**
   ```bash
   # In supervisor configuration
   command=php artisan queue:work --max-time=3600 --memory=512
   ```

2. **Restart Workers Regularly:**
   ```bash
   # Add to crontab
   0 */6 * * * php /var/www/ecommflex/artisan queue:restart
   ```

## Infrastructure Issues

### Server Resource Problems

#### High CPU Usage
**Symptoms:**
- Server response slowdown
- High load averages
- Process timeouts

**Solutions:**
1. **Identify CPU-intensive Processes:**
   ```bash
   top
   htop
   ps aux --sort=-%cpu | head
   ```

2. **Optimize PHP-FPM:**
   ```ini
   ; In php-fpm pool configuration
   pm = dynamic
   pm.max_children = 50
   pm.start_servers = 5
   pm.min_spare_servers = 5
   pm.max_spare_servers = 35
   ```

3. **Enable Caching:**
   ```bash
   # Enable OPcache
   sudo nano /etc/php/8.4/fpm/conf.d/10-opcache.ini
   
   # Configure Redis
   sudo nano /etc/redis/redis.conf
   ```

#### Disk Space Issues
**Symptoms:**
- "No space left on device" errors
- Log files growing too large
- Application crashes

**Solutions:**
1. **Check Disk Usage:**
   ```bash
   df -h
   du -sh /var/www/ecommflex/*
   ```

2. **Clean Up Logs:**
   ```bash
   # Rotate logs
   sudo logrotate -f /etc/logrotate.conf
   
   # Clear old logs
   find /var/log -name "*.log" -mtime +30 -delete
   ```

3. **Clean Application Cache:**
   ```bash
   php artisan cache:clear
   php artisan view:clear
   php artisan config:clear
   ```

### Network Issues

#### SSL Certificate Problems
**Symptoms:**
- SSL certificate expired warnings
- HTTPS not working
- Mixed content errors

**Solutions:**
1. **Renew Certificates:**
   ```bash
   # Check certificate status
   sudo certbot certificates
   
   # Renew certificates
   sudo certbot renew
   
   # Test renewal
   sudo certbot renew --dry-run
   ```

2. **Fix Certificate Configuration:**
   ```nginx
   # In Nginx configuration
   ssl_certificate /etc/letsencrypt/live/ecommflex.com/fullchain.pem;
   ssl_certificate_key /etc/letsencrypt/live/ecommflex.com/privkey.pem;
   ```

#### Load Balancer Issues
**Symptoms:**
- Uneven traffic distribution
- Health check failures
- Connection timeouts

**Solutions:**
1. **Check Health Endpoints:**
   ```bash
   curl -f https://ecommflex.com/health
   ```

2. **Configure Health Checks:**
   ```nginx
   # Health check endpoint
   location /health {
       access_log off;
       return 200 "healthy\n";
       add_header Content-Type text/plain;
   }
   ```

## Monitoring and Alerting

### Log Analysis

#### Application Logs
```bash
# Monitor Laravel logs
tail -f storage/logs/laravel.log

# Search for errors
grep "ERROR" storage/logs/laravel.log

# Monitor specific patterns
tail -f storage/logs/laravel.log | grep "CRITICAL\|ERROR\|EMERGENCY"
```

#### System Logs
```bash
# Monitor system logs
sudo journalctl -f

# Check specific services
sudo journalctl -u nginx -f
sudo journalctl -u mysql -f
sudo journalctl -u redis -f
```

### Performance Monitoring

#### Database Performance
```sql
-- Check slow queries
SELECT * FROM information_schema.processlist WHERE time > 10;

-- Analyze query performance
EXPLAIN SELECT * FROM users WHERE email = '<EMAIL>';

-- Check index usage
SHOW INDEX FROM users;
```

#### Application Performance
```bash
# Monitor PHP-FPM status
curl http://localhost/fpm-status

# Check OPcache status
curl http://localhost/opcache-status

# Monitor Redis performance
redis-cli info stats
```

This troubleshooting guide covers the most common issues you may encounter with the eCommFlex CI/CD pipeline and application deployment. Always check logs first and follow the systematic approach outlined above.
