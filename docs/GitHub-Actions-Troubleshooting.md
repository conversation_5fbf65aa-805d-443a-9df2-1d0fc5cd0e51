# GitHub Actions Workflow Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting information for GitHub Actions workflow issues, specifically addressing the "Context access might be invalid" warnings and related deployment problems.

## Common Issues and Solutions

### 1. "Context access might be invalid" Warnings

#### Issue Description
VS Code PROBLEMS tab shows warnings like:
```
Context access might be invalid: STAGING_SSH_PRIVATE_KEY
Context access might be invalid: PRODUCTION_HOST
```

#### Root Cause
GitHub Actions static analysis cannot verify at parse time whether secrets exist in repository settings. This is expected behavior and does not indicate functional issues.

#### Solutions
1. **Verify Workflow Functionality**: These warnings don't affect workflow execution
2. **Run Validation Script**: Use `./scripts/validate-workflows.sh` to verify configuration
3. **Check Secret Configuration**: Ensure all required secrets are properly configured

### 2. Missing Required Secrets

#### Issue Description
Workflow fails with error: "Missing required secrets: [SECRET_NAME]"

#### Diagnosis Steps
1. Check workflow logs for specific missing secrets
2. Verify repository secret configuration
3. Confirm secret names match exactly (case-sensitive)

#### Solutions

##### For Staging Environment:
```bash
# Required secrets in repository settings:
STAGING_SSH_PRIVATE_KEY  # SSH private key for staging server
STAGING_HOST            # Staging server hostname/IP
STAGING_USER            # SSH username for staging server
STAGING_PATH            # Deployment path on staging server
```

##### For Production Environment:
```bash
# Required secrets in repository settings:
PRODUCTION_SSH_PRIVATE_KEY  # SSH private key for production server
PRODUCTION_HOST            # Production server hostname/IP
PRODUCTION_USER            # SSH username for production server
PRODUCTION_PATH            # Deployment path on production server
```

##### Configuration Steps:
1. Go to repository **Settings** > **Secrets and variables** > **Actions**
2. Click **New repository secret**
3. Add each required secret with exact name and value
4. Verify all secrets are added correctly

### 3. SSH Connection Failures

#### Issue Description
Workflow fails with: "Failed to connect to server" or "SSH connection failed"

#### Common Causes
- Invalid SSH key format
- Incorrect server hostname/IP
- SSH service not running
- Authentication failures

#### Diagnosis Commands
```bash
# Test SSH connection manually
ssh -o ConnectTimeout=10 -o BatchMode=yes user@host "echo 'Connection successful'"

# Check SSH key format
head -1 ~/.ssh/id_rsa  # Should start with -----BEGIN OPENSSH PRIVATE KEY-----

# Test server connectivity
ping hostname
telnet hostname 22
```

#### Solutions

##### 1. SSH Key Issues
```bash
# Generate new SSH key pair
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Copy public key to server
ssh-copy-id user@hostname

# Verify private key format (for GitHub secret)
cat ~/.ssh/id_rsa  # Copy entire content including headers
```

##### 2. Server Configuration
```bash
# Check SSH service status
sudo systemctl status ssh

# Restart SSH service
sudo systemctl restart ssh

# Check SSH configuration
sudo nano /etc/ssh/sshd_config
# Ensure: PubkeyAuthentication yes
```

##### 3. User Permissions
```bash
# Check user exists and has proper permissions
id username

# Add user to necessary groups
sudo usermod -aG www-data username

# Set proper home directory permissions
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

### 4. Environment Configuration Issues

#### Issue Description
Error: "Environment 'staging' not found" or similar environment-related errors

#### Solutions

##### Create GitHub Environments:
1. Go to repository **Settings** > **Environments**
2. Click **New environment**
3. Create the following environments:
   - `staging` (for staging deployments)
   - `approval` (for production approval process)
   - `production` (for production deployments)

##### Configure Environment Protection:
1. Select environment to configure
2. Add **Required reviewers** for production environments
3. Set **Deployment branches** if needed
4. Configure **Environment secrets** if required

### 5. Job Dependency Issues

#### Issue Description
Jobs run in wrong order, skip unexpectedly, or fail due to missing dependencies

#### Common Patterns
```yaml
# Incorrect - missing secret validation dependency
deploy-to-staging:
  needs: [build-for-staging]

# Correct - includes all required dependencies
deploy-to-staging:
  needs: [validate-secrets, pre-deployment-checks, build-for-staging]
  if: needs.validate-secrets.outputs.secrets-available == 'true'
```

#### Solutions
1. **Review Job Dependencies**: Ensure all `needs:` declarations are complete
2. **Check Conditional Logic**: Verify `if:` conditions are correct
3. **Validate Workflow Structure**: Use validation script to check dependencies

### 6. Composite Action Issues

#### Issue Description
Custom composite actions fail or are not found

#### Required Files
```
.github/actions/validate-deployment-secrets/action.yml
.github/actions/setup-deployment-ssh/action.yml
.github/actions/deploy-with-backup/action.yml
```

#### Solutions
1. **Verify File Existence**: Ensure all composite action files exist
2. **Check YAML Syntax**: Validate action file syntax
3. **Test Action Logic**: Review shell scripts in composite actions

## Validation and Testing

### Automated Validation
```bash
# Make script executable
chmod +x scripts/validate-workflows.sh

# Run comprehensive validation
./scripts/validate-workflows.sh

# Check specific workflow syntax
yq eval .github/workflows/deploy-staging.yml
```

### Manual Testing Steps
1. **Test Secret Configuration**:
   - Trigger workflow with missing secrets (should fail gracefully)
   - Verify error messages are clear and helpful

2. **Test SSH Connection**:
   - Use invalid SSH credentials (should fail with clear error)
   - Test with correct credentials (should succeed)

3. **Test Workflow Flow**:
   - Run complete staging deployment
   - Verify all jobs execute in correct order
   - Check conditional logic works properly

### Debug Mode
Enable detailed logging by setting repository secrets:
```
ACTIONS_STEP_DEBUG = true
ACTIONS_RUNNER_DEBUG = true
```

## Best Practices

### 1. Secret Management
- Use descriptive secret names
- Document all required secrets in workflow files
- Regularly rotate SSH keys and secrets
- Use environment-specific secrets when possible

### 2. Error Handling
- Provide clear error messages
- Include troubleshooting steps in error output
- Implement graceful failure modes
- Add validation before critical operations

### 3. Workflow Structure
- Use secret validation jobs
- Implement proper job dependencies
- Add conditional execution based on validation results
- Include comprehensive logging

### 4. Testing
- Test workflows with missing/invalid configuration
- Validate all error paths
- Use validation scripts regularly
- Monitor workflow execution logs

## Getting Additional Help

### Resources
1. **Workflow Logs**: Check detailed logs in GitHub Actions tab
2. **Validation Script**: Run `./scripts/validate-workflows.sh`
3. **Documentation**: Review `docs/GitHub-Actions-Workflow-Fix-Documentation.md`
4. **GitHub Issues**: Search existing issues or create new ones

### Creating Support Requests
When reporting workflow issues, include:
1. **Error Messages**: Complete error output from workflow logs
2. **Workflow Configuration**: Relevant workflow file sections
3. **Environment Details**: Repository settings and environment configuration
4. **Validation Results**: Output from validation script
5. **Steps to Reproduce**: Clear reproduction steps

### Emergency Procedures
For critical deployment failures:
1. **Check Server Status**: Verify server accessibility
2. **Manual Deployment**: Use manual deployment procedures if needed
3. **Rollback**: Use emergency rollback procedures
4. **Incident Response**: Follow incident response procedures for production issues
