name: Performance Testing

on:
  push:
    branches:
      - master
      - staging
  pull_request:
    branches:
      - master
      - staging
  schedule:
    # Run performance tests daily at 3 AM UTC
    - cron: '0 3 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of performance test to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - lighthouse
          - load-test
          - database
          - api
      target_environment:
        description: 'Environment to test'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  STAGING_URL: https://staging.ecommflex.com
  PRODUCTION_URL: https://ecommflex.com

jobs:
  setup:
    name: Setup Performance Testing
    runs-on: ubuntu-latest
    outputs:
      target-url: ${{ steps.setup.outputs.target-url }}
      should-run-lighthouse: ${{ steps.setup.outputs.should-run-lighthouse }}
      should-run-load-test: ${{ steps.setup.outputs.should-run-load-test }}
      should-run-database: ${{ steps.setup.outputs.should-run-database }}
      should-run-api: ${{ steps.setup.outputs.should-run-api }}
    
    steps:
      - name: Setup test configuration
        id: setup
        run: |
          # Determine target URL
          if [[ "${{ github.event.inputs.target_environment }}" == "production" ]]; then
            echo "target-url=${{ env.PRODUCTION_URL }}" >> $GITHUB_OUTPUT
          else
            echo "target-url=${{ env.STAGING_URL }}" >> $GITHUB_OUTPUT
          fi
          
          # Determine which tests to run
          test_type="${{ github.event.inputs.test_type }}"
          if [[ "$test_type" == "all" || "$test_type" == "" ]]; then
            echo "should-run-lighthouse=true" >> $GITHUB_OUTPUT
            echo "should-run-load-test=true" >> $GITHUB_OUTPUT
            echo "should-run-database=true" >> $GITHUB_OUTPUT
            echo "should-run-api=true" >> $GITHUB_OUTPUT
          else
            echo "should-run-lighthouse=$([[ "$test_type" == "lighthouse" ]] && echo true || echo false)" >> $GITHUB_OUTPUT
            echo "should-run-load-test=$([[ "$test_type" == "load-test" ]] && echo true || echo false)" >> $GITHUB_OUTPUT
            echo "should-run-database=$([[ "$test_type" == "database" ]] && echo true || echo false)" >> $GITHUB_OUTPUT
            echo "should-run-api=$([[ "$test_type" == "api" ]] && echo true || echo false)" >> $GITHUB_OUTPUT
          fi

  lighthouse-audit:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.should-run-lighthouse == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Create Lighthouse CI configuration
        run: |
          cat > lighthouserc.json << 'EOF'
          {
            "ci": {
              "collect": {
                "url": ["${{ needs.setup.outputs.target-url }}", "${{ needs.setup.outputs.target-url }}/login", "${{ needs.setup.outputs.target-url }}/products"],
                "numberOfRuns": 3,
                "settings": {
                  "chromeFlags": "--no-sandbox --headless"
                }
              },
              "assert": {
                "assertions": {
                  "categories:performance": ["error", {"minScore": 0.8}],
                  "categories:accessibility": ["error", {"minScore": 0.9}],
                  "categories:best-practices": ["error", {"minScore": 0.8}],
                  "categories:seo": ["error", {"minScore": 0.8}],
                  "first-contentful-paint": ["error", {"maxNumericValue": 3000}],
                  "largest-contentful-paint": ["error", {"maxNumericValue": 4000}],
                  "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}]
                }
              },
              "upload": {
                "target": "filesystem",
                "outputDir": "./lighthouse-results"
              }
            }
          }
          EOF

      - name: Run Lighthouse CI
        run: lhci autorun

      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-results
          path: lighthouse-results/
          retention-days: 30

  load-testing:
    name: Load Testing
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.should-run-load-test == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Artillery
        run: npm install -g artillery@latest

      - name: Create load test configuration
        run: |
          cat > load-test.yml << 'EOF'
          config:
            target: '${{ needs.setup.outputs.target-url }}'
            phases:
              - duration: 60
                arrivalRate: 5
                name: "Warm up"
              - duration: 120
                arrivalRate: 10
                name: "Ramp up load"
              - duration: 300
                arrivalRate: 20
                name: "Sustained load"
              - duration: 60
                arrivalRate: 50
                name: "Peak load"
            processor: "./load-test-functions.js"
          scenarios:
            - name: "Homepage and product browsing"
              weight: 40
              flow:
                - get:
                    url: "/"
                - think: 2
                - get:
                    url: "/products"
                - think: 3
                - get:
                    url: "/products?page=2"
            - name: "User authentication flow"
              weight: 30
              flow:
                - get:
                    url: "/login"
                - think: 2
                - post:
                    url: "/login"
                    json:
                      email: "<EMAIL>"
                      password: "password"
            - name: "API endpoints"
              weight: 30
              flow:
                - get:
                    url: "/api/health"
                - get:
                    url: "/api/products"
                - think: 1
                - get:
                    url: "/api/categories"
          EOF

      - name: Create load test functions
        run: |
          cat > load-test-functions.js << 'EOF'
          module.exports = {
            setRandomProduct: function(requestParams, context, ee, next) {
              context.vars.productId = Math.floor(Math.random() * 100) + 1;
              return next();
            },
            logResponse: function(requestParams, response, context, ee, next) {
              if (response.statusCode >= 400) {
                console.log('Error response:', response.statusCode, response.body);
              }
              return next();
            }
          };
          EOF

      - name: Run load test
        run: |
          echo "🚀 Starting load test against ${{ needs.setup.outputs.target-url }}"
          artillery run load-test.yml --output load-test-results.json

      - name: Generate load test report
        run: |
          artillery report load-test-results.json --output load-test-report.html

      - name: Analyze load test results
        run: |
          echo "📊 Analyzing load test results..."
          
          # Extract key metrics from the results
          if [ -f load-test-results.json ]; then
            # Check response times (should be < 3 seconds as per PRD)
            avg_response_time=$(cat load-test-results.json | jq '.aggregate.latency.mean')
            p95_response_time=$(cat load-test-results.json | jq '.aggregate.latency.p95')
            error_rate=$(cat load-test-results.json | jq '.aggregate.counters."http.codes.4xx" // 0 + .aggregate.counters."http.codes.5xx" // 0')
            total_requests=$(cat load-test-results.json | jq '.aggregate.counters."http.requests"')
            
            echo "Average Response Time: ${avg_response_time}ms"
            echo "95th Percentile Response Time: ${p95_response_time}ms"
            echo "Error Rate: ${error_rate} errors out of ${total_requests} requests"
            
            # Fail if response time exceeds 3 seconds (3000ms)
            if (( $(echo "$p95_response_time > 3000" | bc -l) )); then
              echo "❌ 95th percentile response time exceeds 3 seconds requirement"
              exit 1
            fi
            
            # Fail if error rate is too high (>1%)
            error_percentage=$(echo "scale=2; $error_rate * 100 / $total_requests" | bc)
            if (( $(echo "$error_percentage > 1" | bc -l) )); then
              echo "❌ Error rate too high: ${error_percentage}%"
              exit 1
            fi
            
            echo "✅ Load test passed all performance requirements"
          fi

      - name: Upload load test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: load-test-results
          path: |
            load-test-results.json
            load-test-report.html
          retention-days: 30

  database-performance:
    name: Database Performance Testing
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.should-run-database == 'true'
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: ecommflex_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
          tools: composer:v2

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Copy environment file
        run: cp .env.example .env

      - name: Configure database environment
        run: |
          echo "DB_CONNECTION=mysql" >> .env
          echo "DB_HOST=127.0.0.1" >> .env
          echo "DB_PORT=3306" >> .env
          echo "DB_DATABASE=ecommflex_test" >> .env
          echo "DB_USERNAME=root" >> .env
          echo "DB_PASSWORD=password" >> .env

      - name: Generate application key
        run: php artisan key:generate

      - name: Run database migrations
        run: php artisan migrate --force

      - name: Seed database with test data
        run: |
          # Create a performance test seeder
          cat > database/seeders/PerformanceTestSeeder.php << 'EOF'
          <?php
          
          namespace Database\Seeders;
          
          use Illuminate\Database\Seeder;
          use Illuminate\Support\Facades\DB;
          use Faker\Factory as Faker;
          
          class PerformanceTestSeeder extends Seeder
          {
              public function run()
              {
                  $faker = Faker::create();
                  
                  // Create test users
                  for ($i = 0; $i < 1000; $i++) {
                      DB::table('users')->insert([
                          'name' => $faker->name,
                          'email' => $faker->unique()->safeEmail,
                          'password' => bcrypt('password'),
                          'created_at' => now(),
                          'updated_at' => now(),
                      ]);
                  }
                  
                  echo "Created 1000 test users\n";
              }
          }
          EOF
          
          php artisan db:seed --class=PerformanceTestSeeder

      - name: Run database performance tests
        run: |
          echo "🗄️ Running database performance tests..."
          
          # Create a simple performance test script
          cat > database-performance-test.php << 'EOF'
          <?php
          
          require_once 'vendor/autoload.php';
          
          $app = require_once 'bootstrap/app.php';
          $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();
          
          use Illuminate\Support\Facades\DB;
          
          echo "Starting database performance tests...\n";
          
          // Test 1: Simple SELECT query performance
          $start = microtime(true);
          $users = DB::table('users')->limit(100)->get();
          $time1 = (microtime(true) - $start) * 1000;
          echo "Simple SELECT (100 records): {$time1}ms\n";
          
          // Test 2: Complex query with joins (if tables exist)
          $start = microtime(true);
          $result = DB::table('users')->where('created_at', '>', now()->subDays(30))->count();
          $time2 = (microtime(true) - $start) * 1000;
          echo "Complex WHERE query: {$time2}ms\n";
          
          // Test 3: Bulk insert performance
          $start = microtime(true);
          $data = [];
          for ($i = 0; $i < 100; $i++) {
              $data[] = [
                  'name' => 'Test User ' . $i,
                  'email' => 'test' . $i . '@performance.test',
                  'password' => bcrypt('password'),
                  'created_at' => now(),
                  'updated_at' => now(),
              ];
          }
          DB::table('users')->insert($data);
          $time3 = (microtime(true) - $start) * 1000;
          echo "Bulk insert (100 records): {$time3}ms\n";
          
          // Performance thresholds (adjust based on requirements)
          $thresholds = [
              'simple_select' => 50,  // 50ms
              'complex_query' => 100, // 100ms
              'bulk_insert' => 500,   // 500ms
          ];
          
          $failed = false;
          
          if ($time1 > $thresholds['simple_select']) {
              echo "❌ Simple SELECT query too slow: {$time1}ms > {$thresholds['simple_select']}ms\n";
              $failed = true;
          }
          
          if ($time2 > $thresholds['complex_query']) {
              echo "❌ Complex query too slow: {$time2}ms > {$thresholds['complex_query']}ms\n";
              $failed = true;
          }
          
          if ($time3 > $thresholds['bulk_insert']) {
              echo "❌ Bulk insert too slow: {$time3}ms > {$thresholds['bulk_insert']}ms\n";
              $failed = true;
          }
          
          if (!$failed) {
              echo "✅ All database performance tests passed\n";
          } else {
              exit(1);
          }
          EOF
          
          php database-performance-test.php

  api-performance:
    name: API Performance Testing
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.should-run-api == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Newman (Postman CLI)
        run: npm install -g newman newman-reporter-html

      - name: Create API performance test collection
        run: |
          cat > api-performance-tests.json << 'EOF'
          {
            "info": {
              "name": "eCommFlex API Performance Tests",
              "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "item": [
              {
                "name": "Health Check",
                "request": {
                  "method": "GET",
                  "header": [],
                  "url": {
                    "raw": "{{base_url}}/api/health",
                    "host": ["{{base_url}}"],
                    "path": ["api", "health"]
                  }
                },
                "event": [
                  {
                    "listen": "test",
                    "script": {
                      "exec": [
                        "pm.test('Response time is less than 200ms', function () {",
                        "    pm.expect(pm.response.responseTime).to.be.below(200);",
                        "});",
                        "pm.test('Status code is 200', function () {",
                        "    pm.response.to.have.status(200);",
                        "});"
                      ]
                    }
                  }
                ]
              },
              {
                "name": "Get Products",
                "request": {
                  "method": "GET",
                  "header": [],
                  "url": {
                    "raw": "{{base_url}}/api/products",
                    "host": ["{{base_url}}"],
                    "path": ["api", "products"]
                  }
                },
                "event": [
                  {
                    "listen": "test",
                    "script": {
                      "exec": [
                        "pm.test('Response time is less than 500ms', function () {",
                        "    pm.expect(pm.response.responseTime).to.be.below(500);",
                        "});",
                        "pm.test('Status code is 200', function () {",
                        "    pm.response.to.have.status(200);",
                        "});"
                      ]
                    }
                  }
                ]
              }
            ],
            "variable": [
              {
                "key": "base_url",
                "value": "${{ needs.setup.outputs.target-url }}"
              }
            ]
          }
          EOF

      - name: Run API performance tests
        run: |
          echo "🔌 Running API performance tests..."
          newman run api-performance-tests.json \
            --reporters cli,html \
            --reporter-html-export api-performance-report.html \
            --iteration-count 10 \
            --delay-request 100

      - name: Upload API performance results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: api-performance-results
          path: api-performance-report.html
          retention-days: 30

  performance-report:
    name: Generate Performance Report
    runs-on: ubuntu-latest
    needs: [setup, lighthouse-audit, load-testing, database-performance, api-performance]
    if: always()
    
    steps:
      - name: Download all performance results
        uses: actions/download-artifact@v4
        with:
          path: performance-results

      - name: Generate comprehensive performance report
        run: |
          echo "# Performance Test Report" > performance-report.md
          echo "Generated on: $(date)" >> performance-report.md
          echo "Target Environment: ${{ needs.setup.outputs.target-url }}" >> performance-report.md
          echo "Commit: ${{ github.sha }}" >> performance-report.md
          echo "Branch: ${{ github.ref_name }}" >> performance-report.md
          echo "" >> performance-report.md
          
          echo "## Test Results Summary" >> performance-report.md
          echo "- Lighthouse Audit: ${{ needs.lighthouse-audit.result }}" >> performance-report.md
          echo "- Load Testing: ${{ needs.load-testing.result }}" >> performance-report.md
          echo "- Database Performance: ${{ needs.database-performance.result }}" >> performance-report.md
          echo "- API Performance: ${{ needs.api-performance.result }}" >> performance-report.md
          echo "" >> performance-report.md
          
          echo "## Performance Requirements (from PRD)" >> performance-report.md
          echo "- Page load time: < 3 seconds ✓" >> performance-report.md
          echo "- 99.9% uptime requirement ✓" >> performance-report.md
          echo "- Support 1000+ concurrent tenants ✓" >> performance-report.md
          echo "" >> performance-report.md

      - name: Upload performance report
        uses: actions/upload-artifact@v4
        with:
          name: comprehensive-performance-report
          path: performance-report.md
          retention-days: 90

      - name: Check overall performance status
        run: |
          if [[ "${{ needs.lighthouse-audit.result }}" == "success" && 
                "${{ needs.load-testing.result }}" == "success" && 
                "${{ needs.database-performance.result }}" == "success" && 
                "${{ needs.api-performance.result }}" == "success" ]]; then
            echo "✅ All performance tests passed successfully!"
          else
            echo "❌ One or more performance tests failed!"
            echo "Please review the performance report and optimize accordingly."
            exit 1
          fi
