#!/bin/bash

# Advanced GitHub Actions Workflow Debugging and Fixing Script
# This script detects, analyzes, and provides fixes for GitHub Actions workflow issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Counters
ISSUES_FOUND=0
ISSUES_FIXED=0
WARNINGS=0
RECOMMENDATIONS=0

# Configuration
WORKFLOW_DIR=".github/workflows"
ACTIONS_DIR=".github/actions"
DOCS_DIR="docs"
SCRIPTS_DIR="scripts"

echo -e "${CYAN}🔧 GitHub Actions Workflow Debugger v2.0${NC}"
echo "============================================================"
echo -e "${BLUE}Analyzing workflows for issues and providing fixes...${NC}"
echo ""

# Function to print results
print_issue() {
    echo -e "${RED}❌ ISSUE: $1${NC}"
    ((ISSUES_FOUND++))
}

print_fix() {
    echo -e "${GREEN}✅ FIX: $1${NC}"
    ((ISSUES_FIXED++))
}

print_warning() {
    echo -e "${YELLOW}⚠️ WARNING: $1${NC}"
    ((WARNINGS++))
}

print_recommendation() {
    echo -e "${PURPLE}💡 RECOMMENDATION: $1${NC}"
    ((RECOMMENDATIONS++))
}

print_info() {
    echo -e "${BLUE}ℹ️ INFO: $1${NC}"
}

# Function to check if a file exists
check_file_exists() {
    if [ ! -f "$1" ]; then
        print_issue "Missing file: $1"
        return 1
    fi
    return 0
}

# Function to validate YAML syntax
validate_yaml() {
    local file="$1"
    if command -v yq &> /dev/null; then
        if ! yq eval "$file" > /dev/null 2>&1; then
            print_issue "Invalid YAML syntax in $file"
            return 1
        fi
    elif command -v python3 &> /dev/null; then
        if ! python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            print_issue "Invalid YAML syntax in $file"
            return 1
        fi
    else
        print_warning "No YAML validator found (install yq or python3 with PyYAML)"
        return 0
    fi
    print_fix "YAML syntax valid in $file"
    return 0
}

# Function to check secret usage patterns
check_secret_patterns() {
    local file="$1"
    local secrets_found=()
    
    # Extract secret references
    while IFS= read -r line; do
        if [[ $line =~ \$\{\{[[:space:]]*secrets\.([A-Z_]+)[[:space:]]*\}\} ]]; then
            secrets_found+=("${BASH_REMATCH[1]}")
        fi
    done < "$file"
    
    if [ ${#secrets_found[@]} -gt 0 ]; then
        print_info "Found ${#secrets_found[@]} secret references in $file"
        
        # Check for proper validation patterns
        if ! grep -q "validate-secrets" "$file"; then
            print_issue "Missing secret validation job in $file"
            print_recommendation "Add a secret validation job to check secret availability"
        else
            print_fix "Secret validation job found in $file"
        fi
        
        # Check for conditional execution
        if ! grep -q "needs.*validate-secrets" "$file"; then
            print_issue "Jobs don't depend on secret validation in $file"
            print_recommendation "Add 'needs: validate-secrets' to deployment jobs"
        else
            print_fix "Jobs properly depend on secret validation in $file"
        fi
    fi
}

# Function to check environment configuration
check_environment_config() {
    local file="$1"
    
    if grep -q "environment:" "$file"; then
        local env_names=($(grep -o "name: [a-zA-Z-]*" "$file" | cut -d' ' -f2))
        for env_name in "${env_names[@]}"; do
            print_info "Found environment: $env_name in $file"
            print_recommendation "Ensure GitHub environment '$env_name' is configured in repository settings"
        done
    else
        print_warning "No environment configuration found in $file"
    fi
}

# Function to check job dependencies
check_job_dependencies() {
    local file="$1"
    local jobs=($(grep -o "^[[:space:]]*[a-zA-Z-]*:" "$file" | grep -v "^[[:space:]]*name:" | grep -v "^[[:space:]]*on:" | grep -v "^[[:space:]]*env:" | sed 's/://g' | sed 's/^[[:space:]]*//' | grep -v "^$"))
    
    print_info "Found ${#jobs[@]} jobs in $file"
    
    # Check for proper job ordering
    local has_dependencies=false
    if grep -q "needs:" "$file"; then
        has_dependencies=true
        print_fix "Job dependencies configured in $file"
    else
        print_warning "No job dependencies found in $file"
        print_recommendation "Consider adding job dependencies for proper execution order"
    fi
}

# Function to analyze workflow files
analyze_workflow() {
    local file="$1"
    echo ""
    echo -e "${CYAN}📋 Analyzing: $file${NC}"
    echo "------------------------------------------------------------"
    
    # Basic file checks
    if ! check_file_exists "$file"; then
        return 1
    fi
    
    # YAML syntax validation
    validate_yaml "$file"
    
    # Secret pattern analysis
    check_secret_patterns "$file"
    
    # Environment configuration
    check_environment_config "$file"
    
    # Job dependencies
    check_job_dependencies "$file"
    
    # Specific workflow checks
    if [[ $file == *"deploy-staging"* ]]; then
        check_staging_specific "$file"
    elif [[ $file == *"deploy-production"* ]]; then
        check_production_specific "$file"
    fi
}

# Function to check staging-specific requirements
check_staging_specific() {
    local file="$1"
    
    # Check for staging-specific secrets
    local required_secrets=("STAGING_SSH_PRIVATE_KEY" "STAGING_HOST" "STAGING_USER" "STAGING_PATH")
    for secret in "${required_secrets[@]}"; do
        if grep -q "$secret" "$file"; then
            print_fix "Required staging secret '$secret' referenced in $file"
        else
            print_issue "Missing staging secret '$secret' in $file"
        fi
    done
    
    # Check for staging environment
    if grep -q "name: staging" "$file"; then
        print_fix "Staging environment configured in $file"
    else
        print_issue "Missing staging environment configuration in $file"
    fi
}

# Function to check production-specific requirements
check_production_specific() {
    local file="$1"
    
    # Check for production-specific secrets
    local required_secrets=("PRODUCTION_SSH_PRIVATE_KEY" "PRODUCTION_HOST" "PRODUCTION_USER" "PRODUCTION_PATH")
    for secret in "${required_secrets[@]}"; do
        if grep -q "$secret" "$file"; then
            print_fix "Required production secret '$secret' referenced in $file"
        else
            print_issue "Missing production secret '$secret' in $file"
        fi
    done
    
    # Check for production environment and approval
    if grep -q "name: production" "$file"; then
        print_fix "Production environment configured in $file"
    else
        print_issue "Missing production environment configuration in $file"
    fi
    
    if grep -q "name: approval" "$file"; then
        print_fix "Approval environment configured in $file"
    else
        print_warning "Missing approval environment for production deployment"
    fi
}

# Function to check for composite actions
check_composite_actions() {
    echo ""
    echo -e "${CYAN}🔧 Checking Composite Actions${NC}"
    echo "------------------------------------------------------------"
    
    local recommended_actions=(
        "validate-deployment-secrets"
        "setup-deployment-ssh"
        "deploy-with-backup"
        "health-check"
        "rollback-deployment"
    )
    
    for action in "${recommended_actions[@]}"; do
        local action_path="$ACTIONS_DIR/$action/action.yml"
        if [ -f "$action_path" ]; then
            print_fix "Composite action exists: $action"
            validate_yaml "$action_path"
        else
            print_issue "Missing composite action: $action"
            print_recommendation "Create composite action at $action_path"
        fi
    done
}

# Main analysis
echo -e "${BLUE}🔍 Starting comprehensive workflow analysis...${NC}"

# Check workflow directory
if [ ! -d "$WORKFLOW_DIR" ]; then
    print_issue "Workflow directory not found: $WORKFLOW_DIR"
    exit 1
fi

# Analyze main workflow files
WORKFLOW_FILES=(
    "$WORKFLOW_DIR/deploy-staging.yml"
    "$WORKFLOW_DIR/deploy-production.yml"
    "$WORKFLOW_DIR/ci.yml"
    "$WORKFLOW_DIR/tests.yml"
)

for file in "${WORKFLOW_FILES[@]}"; do
    if [ -f "$file" ]; then
        analyze_workflow "$file"
    else
        print_issue "Missing workflow file: $file"
    fi
done

# Check composite actions
check_composite_actions

# Summary and recommendations
echo ""
echo "============================================================"
echo -e "${CYAN}📊 Analysis Summary${NC}"
echo "============================================================"
echo -e "Issues Found: ${RED}$ISSUES_FOUND${NC}"
echo -e "Issues Fixed: ${GREEN}$ISSUES_FIXED${NC}"
echo -e "Warnings: ${YELLOW}$WARNINGS${NC}"
echo -e "Recommendations: ${PURPLE}$RECOMMENDATIONS${NC}"
echo ""

if [ $ISSUES_FOUND -gt 0 ]; then
    echo -e "${RED}❌ Found $ISSUES_FOUND issues that need attention${NC}"
    echo -e "${BLUE}💡 Run with --fix flag to automatically apply fixes${NC}"
    exit 1
else
    echo -e "${GREEN}✅ All workflow analysis checks passed!${NC}"
    exit 0
fi
