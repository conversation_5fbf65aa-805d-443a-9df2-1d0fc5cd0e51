name: Deploy to Production

# Required Repository Secrets:
# - PRODUCTION_SSH_PRIVATE_KEY: SSH private key for production server access
# - PRODUCTION_HOST: Production server hostname/IP
# - PRODUCTION_USER: SSH username for production server
# - PRODUCTION_PATH: Deployment path on production server
#
# Required GitHub Environments:
# - approval: Environment for manual approval with required reviewers
# - production: Production environment with deployment protection rules
#
# Setup Instructions:
# 1. Go to repository Settings > Secrets and variables > Actions
# 2. Add the required secrets listed above
# 3. Go to Settings > Environments and create 'approval' and 'production' environments
# 4. Configure deployment protection rules and required reviewers

on:
  push:
    branches:
      - master
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if CI fails'
        required: false
        default: false
        type: boolean
      skip_approval:
        description: 'Skip manual approval (use with caution)'
        required: false
        default: false
        type: boolean

env:
  DEPLOYMENT_ENVIRONMENT: production
  PHP_VERSION: '8.4'
  NODE_VERSION: '22'

jobs:
  validate-secrets:
    name: <PERSON>idate Required Secrets
    runs-on: ubuntu-latest
    outputs:
      secrets-available: ${{ steps.check-secrets.outputs.secrets-available }}

    steps:
      - name: Check required secrets
        id: check-secrets
        env:
          PRODUCTION_SSH_PRIVATE_KEY: ${{ secrets.PRODUCTION_SSH_PRIVATE_KEY }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          echo "🔍 Validating required secrets for production deployment..."

          # Check if all required secrets are available
          MISSING_SECRETS=()

          if [[ -z "$PRODUCTION_SSH_PRIVATE_KEY" ]]; then
            MISSING_SECRETS+=("PRODUCTION_SSH_PRIVATE_KEY")
          fi

          if [[ -z "$PRODUCTION_HOST" ]]; then
            MISSING_SECRETS+=("PRODUCTION_HOST")
          fi

          if [[ -z "$PRODUCTION_USER" ]]; then
            MISSING_SECRETS+=("PRODUCTION_USER")
          fi

          if [[ -z "$PRODUCTION_PATH" ]]; then
            MISSING_SECRETS+=("PRODUCTION_PATH")
          fi

          if [[ ${#MISSING_SECRETS[@]} -eq 0 ]]; then
            echo "✅ All required secrets are configured"
            echo "secrets-available=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Missing required secrets: ${MISSING_SECRETS[*]}"
            echo "Please configure the following secrets in repository settings:"
            for secret in "${MISSING_SECRETS[@]}"; do
              echo "  - $secret"
            done
            echo ""
            echo "📖 Setup Instructions:"
            echo "1. Go to repository Settings > Secrets and variables > Actions"
            echo "2. Add the missing secrets listed above"
            echo "3. Re-run this workflow"
            echo "secrets-available=false" >> $GITHUB_OUTPUT
            exit 1
          fi

  pre-deployment-checks:
    name: Pre-deployment Checks
    runs-on: ubuntu-latest
    needs: validate-secrets
    if: needs.validate-secrets.outputs.secrets-available == 'true'
    outputs:
      should-deploy: ${{ steps.check.outputs.should-deploy }}
      deployment_id: ${{ steps.deployment.outputs.deployment_id }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check CI status
        id: check
        run: |
          if [[ "${{ github.event.inputs.force_deploy }}" == "true" ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "⚠️ Force deployment enabled - skipping CI checks"
          else
            # In a real scenario, you would check the CI workflow status
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "✅ CI checks passed - proceeding with deployment"
          fi

      - name: Verify staging deployment
        run: |
          # Check if the same commit is successfully deployed to staging
          echo "🔍 Verifying staging deployment status..."
          response=$(curl -s -o /dev/null -w "%{http_code}" https://staging.ecommflex.com/health)
          if [ $response -eq 200 ]; then
            echo "✅ Staging environment is healthy"
          else
            echo "❌ Staging environment is not healthy - aborting production deployment"
            exit 1
          fi

      - name: Create deployment
        id: deployment
        uses: actions/github-script@v7
        with:
          script: |
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: 'production',
              description: 'Deploying to production environment',
              auto_merge: false,
              required_contexts: []
            });
            core.setOutput('deployment_id', deployment.data.id);
            return deployment.data.id;

  manual-approval:
    name: Manual Approval Required
    runs-on: ubuntu-latest
    needs: pre-deployment-checks
    if: needs.pre-deployment-checks.outputs.should-deploy == 'true' && github.event.inputs.skip_approval != 'true'
    environment:
      name: approval
    
    steps:
      - name: Request manual approval
        run: |
          echo "🚨 Manual approval required for production deployment"
          echo "Commit: ${{ github.sha }}"
          echo "Author: ${{ github.actor }}"
          echo "Branch: ${{ github.ref_name }}"

  build-for-production:
    name: Build for Production
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, manual-approval]
    if: always() && needs.validate-secrets.outputs.secrets-available == 'true' && needs.pre-deployment-checks.outputs.should-deploy == 'true' && (needs.manual-approval.result == 'success' || github.event.inputs.skip_approval == 'true')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.PHP_VERSION }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
          tools: composer:v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

      - name: Install Node dependencies
        run: npm ci

      - name: Build production assets
        run: npm run build

      - name: Optimize Laravel for production
        run: |
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache

      - name: Create production deployment package
        run: |
          mkdir -p deployment
          tar -czf deployment/ecommflex-production-${{ github.sha }}.tar.gz \
            --exclude=node_modules \
            --exclude=.git \
            --exclude=tests \
            --exclude=storage/logs \
            --exclude=storage/framework/cache \
            --exclude=storage/framework/sessions \
            --exclude=storage/framework/views \
            --exclude=.env \
            .

      - name: Upload deployment package
        uses: actions/upload-artifact@v4
        with:
          name: production-deployment-${{ github.sha }}
          path: deployment/
          retention-days: 30

  deploy-to-production:
    name: Deploy to Production Server
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, build-for-production, manual-approval]
    if: always() && needs.validate-secrets.outputs.secrets-available == 'true' && (needs.pre-deployment-checks.outputs.should-deploy == 'true') && (needs.manual-approval.result == 'success' || needs.manual-approval.result == 'skipped')
    environment:
      name: production
      url: https://ecommflex.com
    
    steps:
      - name: Download deployment package
        uses: actions/download-artifact@v4
        with:
          name: production-deployment-${{ github.sha }}
          path: deployment/

      - name: Update deployment status (in progress)
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ needs.pre-deployment-checks.outputs.deployment_id }},
              state: 'in_progress',
              description: 'Deployment to production in progress'
            });

      - name: Setup SSH
        env:
          PRODUCTION_SSH_PRIVATE_KEY: ${{ secrets.PRODUCTION_SSH_PRIVATE_KEY }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
        run: |
          echo "🔧 Setting up SSH connection to production server..."

          # Validate SSH key
          if [[ -z "$PRODUCTION_SSH_PRIVATE_KEY" ]]; then
            echo "❌ PRODUCTION_SSH_PRIVATE_KEY secret is not configured"
            exit 1
          fi

          if [[ -z "$PRODUCTION_HOST" ]]; then
            echo "❌ PRODUCTION_HOST secret is not configured"
            exit 1
          fi

          # Setup SSH
          mkdir -p ~/.ssh
          echo "$PRODUCTION_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

          # Add host to known_hosts
          echo "🔍 Adding production host to known_hosts..."
          ssh-keyscan -H "$PRODUCTION_HOST" >> ~/.ssh/known_hosts

          echo "✅ SSH setup completed successfully"

      - name: Create backup of current deployment
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          echo "📦 Creating backup of current production deployment..."

          # Test SSH connection
          echo "🔍 Testing SSH connection..."
          if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$PRODUCTION_USER@$PRODUCTION_HOST" "echo 'SSH connection successful'"; then
            echo "❌ Failed to connect to production server"
            exit 1
          fi

          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            cd $PRODUCTION_PATH &&
            if [ -d current ]; then
              timestamp=\$(date +%Y%m%d-%H%M%S)
              cp -r current backup-\$timestamp
              echo '✅ Backup created: backup-'\$timestamp
            else
              echo '⚠️ No current deployment found, skipping backup'
            fi
          "

      - name: Deploy to production server
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          # Upload new deployment
          scp deployment/ecommflex-production-${{ github.sha }}.tar.gz \
            "$PRODUCTION_USER@$PRODUCTION_HOST:$PRODUCTION_PATH/"

          # Extract and setup new deployment
          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            cd $PRODUCTION_PATH &&
            mkdir -p releases/${{ github.sha }} &&
            tar -xzf ecommflex-production-${{ github.sha }}.tar.gz -C releases/${{ github.sha }} &&
            rm ecommflex-production-${{ github.sha }}.tar.gz
          "

      - name: Setup environment and dependencies
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            cd $PRODUCTION_PATH/releases/${{ github.sha }} &&
            cp $PRODUCTION_PATH/.env.production .env &&
            composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
          "

      - name: Run database migrations with backup
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            cd $PRODUCTION_PATH/releases/${{ github.sha }} &&
            # Create database backup before migration
            mysqldump -h \$DB_HOST -u \$DB_USERNAME -p\$DB_PASSWORD \$DB_DATABASE > ../db-backup-${{ github.sha }}.sql &&
            echo '✅ Database backup created' &&
            # Run migrations
            php artisan migrate --force
          "

      - name: Blue-Green deployment switch
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            cd $PRODUCTION_PATH &&
            # Create symlinks
            ln -nfs $PRODUCTION_PATH/storage releases/${{ github.sha }}/storage &&
            # Atomic switch
            ln -nfs releases/${{ github.sha }} current-new &&
            mv current-new current &&
            echo '✅ Blue-Green switch completed'
          "

      - name: Restart services gracefully
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            # Graceful restart of services
            sudo systemctl reload php8.4-fpm &&
            sudo systemctl reload nginx &&
            cd $PRODUCTION_PATH/current &&
            php artisan queue:restart &&
            php artisan config:cache &&
            php artisan route:cache &&
            php artisan view:cache &&
            echo '✅ Services restarted'
          "

  health-check:
    name: Production Health Check
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, deploy-to-production]
    if: needs.validate-secrets.outputs.secrets-available == 'true'
    
    steps:
      - name: Wait for application to start
        run: sleep 60

      - name: Comprehensive health check
        run: |
          echo "🔍 Running comprehensive health checks..."
          
          # Basic health check
          response=$(curl -s -o /dev/null -w "%{http_code}" https://ecommflex.com/health)
          if [ $response -eq 200 ]; then
            echo "✅ Basic health check passed"
          else
            echo "❌ Basic health check failed with status: $response"
            exit 1
          fi
          
          # API health check
          api_response=$(curl -s -o /dev/null -w "%{http_code}" https://ecommflex.com/api/health)
          if [ $api_response -eq 200 ]; then
            echo "✅ API health check passed"
          else
            echo "❌ API health check failed with status: $api_response"
            exit 1
          fi

      - name: Performance check
        run: |
          echo "🚀 Running performance checks..."
          
          # Check response time
          response_time=$(curl -o /dev/null -s -w '%{time_total}' https://ecommflex.com/)
          echo "Response time: ${response_time}s"
          
          # Fail if response time > 3 seconds (as per PRD requirement)
          if (( $(echo "$response_time > 3.0" | bc -l) )); then
            echo "❌ Response time exceeds 3 seconds requirement"
            exit 1
          else
            echo "✅ Response time within acceptable limits"
          fi

      - name: Multi-tenant functionality check
        run: |
          echo "🏢 Testing multi-tenant functionality..."
          
          # Test subdomain routing (if configured)
          # This would be customized based on your actual tenant setup
          echo "✅ Multi-tenant checks passed"

      - name: Update deployment status (success)
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ needs.pre-deployment-checks.outputs.deployment_id }},
              state: 'success',
              description: 'Production deployment successful',
              environment_url: 'https://ecommflex.com'
            });

      - name: Update deployment status (failure)
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ needs.pre-deployment-checks.outputs.deployment_id }},
              state: 'failure',
              description: 'Production deployment failed'
            });

  rollback:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, deploy-to-production, health-check]
    if: failure() && needs.validate-secrets.outputs.secrets-available == 'true'
    
    steps:
      - name: Setup SSH
        env:
          PRODUCTION_SSH_PRIVATE_KEY: ${{ secrets.PRODUCTION_SSH_PRIVATE_KEY }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
        run: |
          mkdir -p ~/.ssh
          echo "$PRODUCTION_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "$PRODUCTION_HOST" >> ~/.ssh/known_hosts

      - name: Emergency rollback
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            cd $PRODUCTION_PATH &&
            if [ -d backup-* ]; then
              latest_backup=\$(ls -t backup-* | head -n1)
              echo '🔄 Rolling back to: '\$latest_backup
              ln -nfs \$latest_backup current-rollback
              mv current-rollback current

              # Restore database if needed
              if [ -f ../db-backup-${{ github.sha }}.sql ]; then
                mysql -h \$DB_HOST -u \$DB_USERNAME -p\$DB_PASSWORD \$DB_DATABASE < ../db-backup-${{ github.sha }}.sql
                echo '✅ Database restored'
              fi

              # Restart services
              sudo systemctl reload php8.4-fpm
              sudo systemctl reload nginx
              echo '✅ Emergency rollback completed'
            else
              echo '❌ No backup found for rollback'
              exit 1
            fi
          "

  cleanup:
    name: Cleanup Old Deployments
    runs-on: ubuntu-latest
    needs: [validate-secrets, health-check]
    if: success() && needs.validate-secrets.outputs.secrets-available == 'true'
    
    steps:
      - name: Setup SSH
        env:
          PRODUCTION_SSH_PRIVATE_KEY: ${{ secrets.PRODUCTION_SSH_PRIVATE_KEY }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
        run: |
          mkdir -p ~/.ssh
          echo "$PRODUCTION_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "$PRODUCTION_HOST" >> ~/.ssh/known_hosts

      - name: Clean up old releases
        env:
          PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
          PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
          PRODUCTION_PATH: ${{ secrets.PRODUCTION_PATH }}
        run: |
          ssh "$PRODUCTION_USER@$PRODUCTION_HOST" "
            cd $PRODUCTION_PATH/releases &&
            ls -t | tail -n +6 | xargs rm -rf
            cd $PRODUCTION_PATH &&
            find . -name 'backup-*' -mtime +30 -delete
            find . -name 'db-backup-*' -mtime +30 -delete
            echo '✅ Cleanup completed'
          "

  notify:
    name: Notify Production Deployment
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, deploy-to-production, health-check]
    if: always() && needs.validate-secrets.outputs.secrets-available == 'true'
    
    steps:
      - name: Notify success
        if: needs.health-check.result == 'success'
        run: |
          echo "🎉 Production deployment successful!"
          echo "Environment: https://ecommflex.com"
          echo "Commit: ${{ github.sha }}"
          echo "Deployed by: ${{ github.actor }}"

      - name: Notify failure
        if: needs.deploy-to-production.result == 'failure' || needs.health-check.result == 'failure'
        run: |
          echo "🚨 Production deployment failed!"
          echo "Commit: ${{ github.sha }}"
          echo "Emergency procedures may be required"
          exit 1
