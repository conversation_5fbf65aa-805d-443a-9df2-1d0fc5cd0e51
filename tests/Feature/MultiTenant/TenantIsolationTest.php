<?php

namespace Tests\Feature\MultiTenant;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

/**
 * @group multi-tenant
 * @group integration
 */
class TenantIsolationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up multi-tenant testing environment
        Config::set('database.connections.tenant_test_1', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
            'foreign_key_constraints' => true,
        ]);
        
        Config::set('database.connections.tenant_test_2', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
            'foreign_key_constraints' => true,
        ]);
    }

    /** @test */
    public function tenant_data_is_completely_isolated()
    {
        // Create users in different tenant databases
        $this->switchToTenant('tenant_test_1');
        $this->artisan('migrate', ['--database' => 'tenant_test_1']);
        
        $tenant1User = User::create([
            'name' => 'Tenant 1 User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $this->switchToTenant('tenant_test_2');
        $this->artisan('migrate', ['--database' => 'tenant_test_2']);
        
        $tenant2User = User::create([
            'name' => 'Tenant 2 User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Verify isolation: Tenant 1 should not see Tenant 2's data
        $this->switchToTenant('tenant_test_1');
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);

        // Verify isolation: Tenant 2 should not see Tenant 1's data
        $this->switchToTenant('tenant_test_2');
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    /** @test */
    public function tenant_cannot_access_another_tenants_data_via_api()
    {
        // Set up tenant 1
        $this->switchToTenant('tenant_test_1');
        $this->artisan('migrate', ['--database' => 'tenant_test_1']);
        
        $tenant1User = User::create([
            'name' => 'Tenant 1 User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Set up tenant 2
        $this->switchToTenant('tenant_test_2');
        $this->artisan('migrate', ['--database' => 'tenant_test_2']);
        
        $tenant2User = User::create([
            'name' => 'Tenant 2 User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        // Authenticate as tenant 1 user
        $this->switchToTenant('tenant_test_1');
        $this->actingAs($tenant1User);

        // Try to access tenant 2's data (should fail)
        $response = $this->getJson('/api/users/' . $tenant2User->id);
        $response->assertStatus(404); // Should not find the user

        // Verify tenant 1 can access their own data
        $response = $this->getJson('/api/users/' . $tenant1User->id);
        $response->assertStatus(200);
        $response->assertJson(['email' => '<EMAIL>']);
    }

    /** @test */
    public function tenant_database_connections_are_properly_switched()
    {
        $this->switchToTenant('tenant_test_1');
        $currentConnection = DB::getDefaultConnection();
        $this->assertEquals('tenant_test_1', $currentConnection);

        $this->switchToTenant('tenant_test_2');
        $currentConnection = DB::getDefaultConnection();
        $this->assertEquals('tenant_test_2', $currentConnection);
    }

    /** @test */
    public function tenant_cache_is_properly_scoped()
    {
        $this->switchToTenant('tenant_test_1');
        cache()->put('test_key', 'tenant_1_value');

        $this->switchToTenant('tenant_test_2');
        cache()->put('test_key', 'tenant_2_value');

        // Switch back to tenant 1 and verify cache isolation
        $this->switchToTenant('tenant_test_1');
        $this->assertEquals('tenant_1_value', cache()->get('test_key'));

        // Verify tenant 2 cache is separate
        $this->switchToTenant('tenant_test_2');
        $this->assertEquals('tenant_2_value', cache()->get('test_key'));
    }

    /** @test */
    public function tenant_sessions_are_properly_isolated()
    {
        $this->switchToTenant('tenant_test_1');
        session(['tenant_data' => 'tenant_1_session']);

        $this->switchToTenant('tenant_test_2');
        session(['tenant_data' => 'tenant_2_session']);

        // Verify session isolation
        $this->switchToTenant('tenant_test_1');
        $this->assertEquals('tenant_1_session', session('tenant_data'));

        $this->switchToTenant('tenant_test_2');
        $this->assertEquals('tenant_2_session', session('tenant_data'));
    }

    /** @test */
    public function tenant_file_storage_is_properly_scoped()
    {
        $this->switchToTenant('tenant_test_1');
        $tenant1Path = storage_path('app/tenant_test_1/test.txt');
        
        $this->switchToTenant('tenant_test_2');
        $tenant2Path = storage_path('app/tenant_test_2/test.txt');

        // Verify different storage paths for different tenants
        $this->assertNotEquals($tenant1Path, $tenant2Path);
        $this->assertStringContains('tenant_test_1', $tenant1Path);
        $this->assertStringContains('tenant_test_2', $tenant2Path);
    }

    /** @test */
    public function tenant_queue_jobs_are_properly_scoped()
    {
        $this->switchToTenant('tenant_test_1');
        
        // Dispatch a job and verify it contains tenant context
        $job = new \App\Jobs\TestTenantJob('test_data');
        
        // Mock the job dispatch to verify tenant context is preserved
        $this->expectsJobs(\App\Jobs\TestTenantJob::class);
        
        dispatch($job);
    }

    /** @test */
    public function cross_tenant_data_leakage_prevention()
    {
        // Create data in tenant 1
        $this->switchToTenant('tenant_test_1');
        $this->artisan('migrate', ['--database' => 'tenant_test_1']);
        
        $sensitiveData = [
            'name' => 'Confidential User',
            'email' => '<EMAIL>',
            'password' => bcrypt('secret'),
        ];
        
        $tenant1User = User::create($sensitiveData);

        // Switch to tenant 2 and try various methods to access tenant 1 data
        $this->switchToTenant('tenant_test_2');
        $this->artisan('migrate', ['--database' => 'tenant_test_2']);

        // Direct database query should not find tenant 1 data
        $this->assertNull(User::where('email', '<EMAIL>')->first());

        // Raw database query should not find tenant 1 data
        $result = DB::select("SELECT * FROM users WHERE email = ?", ['<EMAIL>']);
        $this->assertEmpty($result);

        // Model query with different connection should not work
        $this->assertNull(User::on('tenant_test_1')->where('email', '<EMAIL>')->first());
    }

    /** @test */
    public function tenant_middleware_properly_sets_context()
    {
        // Simulate subdomain-based tenant resolution
        $response = $this->withHeaders([
            'Host' => 'tenant1.ecommflex.com'
        ])->get('/');

        // Verify tenant context is set (this would depend on your middleware implementation)
        $this->assertEquals('tenant1', app('current_tenant'));
    }

    /** @test */
    public function tenant_database_migrations_are_isolated()
    {
        // Run migrations for tenant 1
        $this->switchToTenant('tenant_test_1');
        $this->artisan('migrate', ['--database' => 'tenant_test_1']);
        
        // Verify tables exist in tenant 1
        $this->assertTrue(DB::connection('tenant_test_1')->getSchemaBuilder()->hasTable('users'));

        // Verify tables don't exist in tenant 2 (before migration)
        $this->assertFalse(DB::connection('tenant_test_2')->getSchemaBuilder()->hasTable('users'));

        // Run migrations for tenant 2
        $this->switchToTenant('tenant_test_2');
        $this->artisan('migrate', ['--database' => 'tenant_test_2']);
        
        // Now verify tables exist in tenant 2
        $this->assertTrue(DB::connection('tenant_test_2')->getSchemaBuilder()->hasTable('users'));
    }

    /**
     * Switch to a specific tenant context
     */
    private function switchToTenant(string $tenant): void
    {
        Config::set('database.default', $tenant);
        DB::purge($tenant);
        DB::reconnect($tenant);
        
        // Set tenant context in application
        app()->instance('current_tenant', $tenant);
        
        // Update cache prefix for tenant isolation
        Config::set('cache.prefix', $tenant . '_');
    }
}
