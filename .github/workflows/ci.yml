name: Continuous Integration

on:
  push:
    branches:
      - master
      - staging
      - firozanam
      - mir-ratul
  pull_request:
    branches:
      - master
      - staging
      - firozanam
      - mir-ratul

env:
  CACHE_DRIVER: array
  SESSION_DRIVER: array
  QUEUE_DRIVER: sync

jobs:
  prepare:
    name: Prepare CI Environment
    runs-on: ubuntu-latest
    outputs:
      php-versions: ${{ steps.setup.outputs.php-versions }}
      node-version: ${{ steps.setup.outputs.node-version }}
      should-deploy: ${{ steps.setup.outputs.should-deploy }}
    
    steps:
      - name: Setup CI configuration
        id: setup
        run: |
          echo "php-versions=[\"8.2\", \"8.4\"]" >> $GITHUB_OUTPUT
          echo "node-version=22" >> $GITHUB_OUTPUT
          if [[ "${{ github.ref }}" == "refs/heads/master" || "${{ github.ref }}" == "refs/heads/staging" ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
          else
            echo "should-deploy=false" >> $GITHUB_OUTPUT
          fi

  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    needs: prepare
    
    strategy:
      fail-fast: false
      matrix:
        php-version: ${{ fromJson(needs.prepare.outputs.php-versions) }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
          tools: composer:v2

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: ~/.composer/cache/files
          key: composer-${{ matrix.php-version }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            composer-${{ matrix.php-version }}-
            composer-

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.prepare.outputs.node-version }}
          cache: 'npm'

      - name: Install Node dependencies
        run: npm ci

      - name: Run PHP Pint (Code Style)
        run: vendor/bin/pint --test

      - name: Run ESLint
        run: npm run lint

      - name: Check Prettier formatting
        run: npm run format:check

      - name: Check TypeScript types
        run: npm run types

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: prepare
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          tools: composer:v2

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.prepare.outputs.node-version }}
          cache: 'npm'

      - name: Install Node dependencies
        run: npm ci

      - name: Run PHP Security Checker
        run: |
          curl -H "Accept: text/plain" https://security.symfony.com/check_lock -F lock=@composer.lock

      - name: Run npm audit
        run: npm audit --audit-level=moderate

      - name: Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD
          extra_args: --debug --only-verified

  unit-tests:
    name: Unit Tests (PHP ${{ matrix.php-version }})
    runs-on: ubuntu-latest
    needs: [prepare, code-quality]
    
    strategy:
      fail-fast: false
      matrix:
        php-version: ${{ fromJson(needs.prepare.outputs.php-versions) }}
        
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: ecommflex_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
          coverage: xdebug
          tools: composer:v2

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: ~/.composer/cache/files
          key: composer-${{ matrix.php-version }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            composer-${{ matrix.php-version }}-
            composer-

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Create SQLite database
        run: touch database/database.sqlite

      - name: Copy environment file
        run: cp .env.example .env

      - name: Generate application key
        run: php artisan key:generate

      - name: Run database migrations
        run: php artisan migrate --force

      - name: Run PHPUnit tests with coverage
        run: vendor/bin/phpunit --coverage-clover=coverage.xml --coverage-text

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          flags: unit-tests
          name: unit-tests-php-${{ matrix.php-version }}

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [prepare, unit-tests]
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: ecommflex_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, pdo_mysql, bcmath, soap, intl, gd, exif, iconv, redis
          tools: composer:v2

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.prepare.outputs.node-version }}
          cache: 'npm'

      - name: Install Node dependencies
        run: npm ci

      - name: Build frontend assets
        run: npm run build

      - name: Copy environment file
        run: cp .env.example .env

      - name: Configure MySQL and Redis environment
        run: |
          echo "DB_CONNECTION=mysql" >> .env
          echo "DB_HOST=127.0.0.1" >> .env
          echo "DB_PORT=3306" >> .env
          echo "DB_DATABASE=ecommflex_test" >> .env
          echo "DB_USERNAME=root" >> .env
          echo "DB_PASSWORD=password" >> .env
          echo "REDIS_HOST=127.0.0.1" >> .env
          echo "REDIS_PORT=6379" >> .env
          echo "CACHE_DRIVER=redis" >> .env
          echo "SESSION_DRIVER=redis" >> .env
          echo "QUEUE_CONNECTION=redis" >> .env

      - name: Generate application key
        run: php artisan key:generate

      - name: Run database migrations
        run: php artisan migrate --force

      - name: Run integration tests
        run: vendor/bin/phpunit --group=integration

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [prepare, code-quality, security-scan]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          tools: composer:v2

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.prepare.outputs.node-version }}
          cache: 'npm'

      - name: Install Node dependencies
        run: npm ci

      - name: Build production assets
        run: npm run build

      - name: Create deployment artifact
        if: needs.prepare.outputs.should-deploy == 'true'
        run: |
          mkdir -p artifacts
          tar -czf artifacts/ecommflex-${{ github.sha }}.tar.gz \
            --exclude=node_modules \
            --exclude=.git \
            --exclude=tests \
            --exclude=storage/logs \
            --exclude=storage/framework/cache \
            --exclude=storage/framework/sessions \
            --exclude=storage/framework/views \
            .

      - name: Upload build artifact
        if: needs.prepare.outputs.should-deploy == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: ecommflex-build-${{ github.sha }}
          path: artifacts/
          retention-days: 30

  notify:
    name: Notify CI Results
    runs-on: ubuntu-latest
    needs: [code-quality, security-scan, unit-tests, integration-tests, build]
    if: always()
    
    steps:
      - name: Notify success
        if: needs.code-quality.result == 'success' && needs.security-scan.result == 'success' && needs.unit-tests.result == 'success' && needs.integration-tests.result == 'success' && needs.build.result == 'success'
        run: |
          echo "✅ All CI checks passed successfully!"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"

      - name: Notify failure
        if: needs.code-quality.result == 'failure' || needs.security-scan.result == 'failure' || needs.unit-tests.result == 'failure' || needs.integration-tests.result == 'failure' || needs.build.result == 'failure'
        run: |
          echo "❌ CI checks failed!"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Failed jobs:"
          echo "- Code Quality: ${{ needs.code-quality.result }}"
          echo "- Security Scan: ${{ needs.security-scan.result }}"
          echo "- Unit Tests: ${{ needs.unit-tests.result }}"
          echo "- Integration Tests: ${{ needs.integration-tests.result }}"
          echo "- Build: ${{ needs.build.result }}"
          exit 1
