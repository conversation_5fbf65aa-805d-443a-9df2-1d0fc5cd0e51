APP_NAME=eCommFlex
APP_ENV=testing
APP_KEY=base64:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# CI Database Configuration
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Alternative MySQL configuration for integration tests
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=ecommflex_test
# DB_USERNAME=root
# DB_PASSWORD=password

SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=array
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=array
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Testing specific configurations
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false

# Multi-tenant testing configuration
TENANT_DATABASE_PREFIX=tenant_test_
TENANT_DOMAIN_SUFFIX=.test.local

# Performance testing thresholds
PERFORMANCE_MAX_RESPONSE_TIME=3000
PERFORMANCE_MAX_MEMORY_USAGE=128
PERFORMANCE_MAX_DB_QUERIES=50

# Security testing configuration
SECURITY_SCAN_ENABLED=true
VULNERABILITY_SCAN_ENABLED=true

# Feature flags for testing
FEATURE_MULTI_TENANT=true
FEATURE_PAYMENT_GATEWAYS=false
FEATURE_ADVANCED_ANALYTICS=false
FEATURE_API_RATE_LIMITING=true

# Test data configuration
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=password123
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=admin123

# CI/CD specific settings
CI_ENVIRONMENT=true
SKIP_MIGRATIONS=false
SKIP_SEEDERS=false
PARALLEL_TESTING=true
