name: Security Scanning

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches:
      - master
      - staging
  pull_request:
    branches:
      - master
      - staging
  workflow_dispatch:
    inputs:
      scan_type:
        description: 'Type of security scan to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - dependencies
          - sast
          - secrets
          - container

permissions:
  contents: read
  security-events: write
  actions: read

jobs:
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'all' || github.event.inputs.scan_type == 'dependencies' || github.event.inputs.scan_type == ''
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          tools: composer:v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Install Node dependencies
        run: npm ci

      - name: Run PHP Security Checker
        run: |
          echo "🔍 Scanning PHP dependencies for vulnerabilities..."
          curl -H "Accept: text/plain" https://security.symfony.com/check_lock -F lock=@composer.lock > php-security-report.txt
          if grep -q "No packages have known vulnerabilities" php-security-report.txt; then
            echo "✅ No PHP vulnerabilities found"
          else
            echo "❌ PHP vulnerabilities detected:"
            cat php-security-report.txt
            exit 1
          fi

      - name: Run npm audit
        run: |
          echo "🔍 Scanning Node.js dependencies for vulnerabilities..."
          npm audit --audit-level=moderate --json > npm-audit-report.json || true
          
          # Check if there are any moderate or higher vulnerabilities
          vulnerabilities=$(cat npm-audit-report.json | jq '.metadata.vulnerabilities.moderate + .metadata.vulnerabilities.high + .metadata.vulnerabilities.critical')
          
          if [ "$vulnerabilities" -gt 0 ]; then
            echo "❌ Found $vulnerabilities moderate or higher vulnerabilities in Node.js dependencies"
            npm audit --audit-level=moderate
            exit 1
          else
            echo "✅ No significant Node.js vulnerabilities found"
          fi

      - name: Upload dependency scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: dependency-scan-results
          path: |
            php-security-report.txt
            npm-audit-report.json
          retention-days: 30

  sast-scan:
    name: Static Application Security Testing
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'all' || github.event.inputs.scan_type == 'sast' || github.event.inputs.scan_type == ''
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          tools: composer:v2

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Install PHPStan for security analysis
        run: |
          composer require --dev phpstan/phpstan
          composer require --dev phpstan/phpstan-laravel

      - name: Create PHPStan security configuration
        run: |
          cat > phpstan-security.neon << 'EOF'
          parameters:
              level: 8
              paths:
                  - app
              excludePaths:
                  - app/Console/Kernel.php
              checkMissingIterableValueType: false
              checkGenericClassInNonGenericObjectType: false
              rules:
                  - PHPStan\Rules\Security\*
          EOF

      - name: Run PHPStan security analysis
        run: |
          echo "🔍 Running static security analysis..."
          vendor/bin/phpstan analyse --configuration=phpstan-security.neon --error-format=json > phpstan-security-report.json || true
          
          # Check for security-related issues
          if [ -s phpstan-security-report.json ]; then
            errors=$(cat phpstan-security-report.json | jq '.totals.errors')
            if [ "$errors" -gt 0 ]; then
              echo "❌ Found $errors security issues in code"
              vendor/bin/phpstan analyse --configuration=phpstan-security.neon
              exit 1
            fi
          fi
          echo "✅ No security issues found in static analysis"

      - name: Install Semgrep
        run: |
          echo "📦 Installing Semgrep..."
          pip install semgrep

      - name: Run Semgrep SAST
        run: |
          echo "🔍 Running Semgrep SAST scan..."
          semgrep scan \
            --config=p/security-audit \
            --config=p/secrets \
            --config=p/php \
            --config=p/javascript \
            --sarif \
            --output=semgrep.sarif \
            --quiet || true

          # Check if SARIF file was generated
          if [ -f semgrep.sarif ]; then
            findings=$(cat semgrep.sarif | jq '.runs[0].results | length')
            if [ "$findings" -gt 0 ]; then
              echo "⚠️ Found $findings security findings in SAST scan"
            else
              echo "✅ No security findings detected in SAST scan"
            fi
          else
            echo "❌ Failed to generate SARIF report"
            touch semgrep.sarif  # Create empty file for upload
          fi

      - name: Upload SARIF file
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: semgrep.sarif
        if: always()

      - name: Upload SAST results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: sast-scan-results
          path: |
            phpstan-security-report.json
            semgrep.sarif
          retention-days: 30

  secrets-scan:
    name: Secrets Detection
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'all' || github.event.inputs.scan_type == 'secrets' || github.event.inputs.scan_type == ''
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog secrets scan
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD
          extra_args: --debug --only-verified --json --output=trufflehog-report.json

      - name: Check for secrets
        run: |
          if [ -f trufflehog-report.json ] && [ -s trufflehog-report.json ]; then
            echo "❌ Secrets detected in repository!"
            cat trufflehog-report.json
            exit 1
          else
            echo "✅ No secrets detected"
          fi

      - name: Upload secrets scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: secrets-scan-results
          path: trufflehog-report.json
          retention-days: 30

  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'all' || github.event.inputs.scan_type == 'container' || github.event.inputs.scan_type == ''
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker image for scanning
        run: |
          # Create a basic Dockerfile for scanning if it doesn't exist
          if [ ! -f Dockerfile ]; then
            cat > Dockerfile << 'EOF'
          FROM php:8.4-fpm-alpine
          
          # Install system dependencies
          RUN apk add --no-cache \
              git \
              curl \
              libpng-dev \
              oniguruma-dev \
              libxml2-dev \
              zip \
              unzip \
              nodejs \
              npm
          
          # Install PHP extensions
          RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd
          
          # Install Composer
          COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
          
          # Set working directory
          WORKDIR /var/www
          
          # Copy application files
          COPY . .
          
          # Install dependencies
          RUN composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
          RUN npm ci && npm run build
          
          # Set permissions
          RUN chown -R www-data:www-data /var/www
          
          EXPOSE 9000
          CMD ["php-fpm"]
          EOF
          fi
          
          docker build -t ecommflex:security-scan .

      - name: Run Trivy container scan
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'ecommflex:security-scan'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run Trivy for critical vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'ecommflex:security-scan'
          format: 'table'
          severity: 'CRITICAL,HIGH'
          exit-code: '1'

  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, sast-scan, secrets-scan, container-scan]
    if: always()
    
    steps:
      - name: Download all scan results
        uses: actions/download-artifact@v4
        with:
          path: security-results

      - name: Generate comprehensive security report
        run: |
          echo "# Security Scan Report" > security-report.md
          echo "Generated on: $(date)" >> security-report.md
          echo "Commit: ${{ github.sha }}" >> security-report.md
          echo "Branch: ${{ github.ref_name }}" >> security-report.md
          echo "" >> security-report.md
          
          echo "## Scan Results Summary" >> security-report.md
          echo "- Dependency Scan: ${{ needs.dependency-scan.result }}" >> security-report.md
          echo "- SAST Scan: ${{ needs.sast-scan.result }}" >> security-report.md
          echo "- Secrets Scan: ${{ needs.secrets-scan.result }}" >> security-report.md
          echo "- Container Scan: ${{ needs.container-scan.result }}" >> security-report.md
          echo "" >> security-report.md
          
          # Add detailed results if available
          if [ -d "security-results" ]; then
            echo "## Detailed Results" >> security-report.md
            find security-results -name "*.txt" -o -name "*.json" | while read file; do
              echo "### $(basename $file)" >> security-report.md
              echo '```' >> security-report.md
              head -50 "$file" >> security-report.md
              echo '```' >> security-report.md
              echo "" >> security-report.md
            done
          fi

      - name: Upload security report
        uses: actions/upload-artifact@v4
        with:
          name: comprehensive-security-report
          path: security-report.md
          retention-days: 90

      - name: Check overall security status
        run: |
          if [[ "${{ needs.dependency-scan.result }}" == "success" && 
                "${{ needs.sast-scan.result }}" == "success" && 
                "${{ needs.secrets-scan.result }}" == "success" && 
                "${{ needs.container-scan.result }}" == "success" ]]; then
            echo "✅ All security scans passed successfully!"
          else
            echo "❌ One or more security scans failed!"
            echo "Please review the security report and address any issues."
            exit 1
          fi

  notify-security-team:
    name: Notify Security Team
    runs-on: ubuntu-latest
    needs: [security-report]
    if: failure() && github.ref == 'refs/heads/master'
    
    steps:
      - name: Notify security team of failures
        run: |
          echo "🚨 Security scan failures detected on master branch!"
          echo "Immediate attention required."
          echo "Commit: ${{ github.sha }}"
          echo "Author: ${{ github.actor }}"
          # In a real scenario, you would send notifications to Slack, email, etc.
