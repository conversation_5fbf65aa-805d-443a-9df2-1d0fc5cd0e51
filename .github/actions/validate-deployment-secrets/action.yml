name: 'Validate Deployment Secrets'
description: 'Validates required deployment secrets and provides clear error messages'
author: 'EcommFlex Team'
inputs:
  environment:
    description: 'Deployment environment (staging or production)'
    required: true
  ssh_private_key:
    description: 'SSH private key secret'
    required: true
  host:
    description: 'Deployment host secret'
    required: true
  user:
    description: 'SSH user secret'
    required: true
  path:
    description: 'Deployment path secret'
    required: true
outputs:
  secrets-available:
    description: 'Whether all required secrets are available'
    value: ${{ steps.validate.outputs.secrets-available }}
  missing-secrets:
    description: 'List of missing secrets'
    value: ${{ steps.validate.outputs.missing-secrets }}
runs:
  using: 'composite'
  steps:
    - name: Validate deployment secrets
      id: validate
      shell: bash
      env:
        SSH_PRIVATE_KEY: ${{ inputs.ssh_private_key }}
        HOST: ${{ inputs.host }}
        USER: ${{ inputs.user }}
        PATH: ${{ inputs.path }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🔍 Validating required secrets for $ENVIRONMENT deployment..."

        # Initialize arrays for tracking
        MISSING_SECRETS=()
        INVALID_SECRETS=()

        # Check SSH private key
        if [[ -z "$SSH_PRIVATE_KEY" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_SSH_PRIVATE_KEY")
        elif [[ ! "$SSH_PRIVATE_KEY" =~ ^-----BEGIN.*PRIVATE.KEY----- ]]; then
          INVALID_SECRETS+=("${ENVIRONMENT^^}_SSH_PRIVATE_KEY (invalid format)")
        fi

        # Check host
        if [[ -z "$HOST" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_HOST")
        elif [[ ! "$HOST" =~ ^[a-zA-Z0-9.-]+$ ]]; then
          INVALID_SECRETS+=("${ENVIRONMENT^^}_HOST (invalid format)")
        fi

        # Check user
        if [[ -z "$USER" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_USER")
        elif [[ ! "$USER" =~ ^[a-zA-Z0-9_-]+$ ]]; then
          INVALID_SECRETS+=("${ENVIRONMENT^^}_USER (invalid format)")
        fi

        # Check path
        if [[ -z "$PATH" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_PATH")
        elif [[ ! "$PATH" =~ ^/[a-zA-Z0-9/_-]+$ ]]; then
          INVALID_SECRETS+=("${ENVIRONMENT^^}_PATH (invalid format)")
        fi
        
        # Report results
        if [[ ${#MISSING_SECRETS[@]} -eq 0 && ${#INVALID_SECRETS[@]} -eq 0 ]]; then
          echo "✅ All required secrets are configured and valid"
          echo "secrets-available=true" >> $GITHUB_OUTPUT
          echo "missing-secrets=" >> $GITHUB_OUTPUT
        else
          echo "❌ Secret validation failed"

          if [[ ${#MISSING_SECRETS[@]} -gt 0 ]]; then
            echo "Missing secrets: ${MISSING_SECRETS[*]}"
            echo "missing-secrets=${MISSING_SECRETS[*]}" >> $GITHUB_OUTPUT
          fi

          if [[ ${#INVALID_SECRETS[@]} -gt 0 ]]; then
            echo "Invalid secrets: ${INVALID_SECRETS[*]}"
          fi

          echo ""
          echo "📖 Setup Instructions:"
          echo "1. Go to repository Settings > Secrets and variables > Actions"
          echo "2. Add/fix the following secrets:"

          for secret in "${MISSING_SECRETS[@]}" "${INVALID_SECRETS[@]}"; do
            echo "   - $secret"
          done

          echo ""
          echo "📋 Secret Requirements:"
          echo "   - SSH_PRIVATE_KEY: Valid SSH private key (PEM format)"
          echo "   - HOST: Valid hostname or IP address"
          echo "   - USER: Valid SSH username"
          echo "   - PATH: Valid absolute path on target server"

          echo "secrets-available=false" >> $GITHUB_OUTPUT
          exit 1
        fi

    - name: Test secret accessibility
      shell: bash
      env:
        SSH_PRIVATE_KEY: ${{ inputs.ssh_private_key }}
        HOST: ${{ inputs.host }}
        USER: ${{ inputs.user }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🔧 Testing SSH key format and basic connectivity..."

        # Create temporary SSH key file for testing
        temp_key=$(mktemp)
        echo "$SSH_PRIVATE_KEY" > "$temp_key"
        chmod 600 "$temp_key"

        # Validate SSH key format
        if ! ssh-keygen -l -f "$temp_key" &>/dev/null; then
          echo "❌ SSH private key format is invalid"
          rm -f "$temp_key"
          exit 1
        fi

        echo "✅ SSH private key format is valid"

        # Test basic connectivity (without authentication)
        if timeout 10 nc -z "$HOST" 22 2>/dev/null; then
          echo "✅ SSH port (22) is accessible on $HOST"
        else
          echo "⚠️ Cannot reach SSH port on $HOST (this may be expected if using non-standard port)"
        fi

        # Cleanup
        rm -f "$temp_key"

        echo "✅ Secret accessibility tests completed"

branding:
  icon: 'shield'
  color: 'green'
