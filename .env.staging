APP_NAME=eCommFlex
APP_ENV=staging
APP_KEY=base64:CHANGE_THIS_IN_PRODUCTION_STAGING_KEY_HERE
APP_DEBUG=true
APP_URL=https://staging.ecommflex.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single,slack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=info

# Staging Database Configuration
DB_CONNECTION=mysql
DB_HOST=staging-db.ecommflex.com
DB_PORT=3306
DB_DATABASE=ecommflex_staging
DB_USERNAME=ecommflex_staging
DB_PASSWORD=CHANGE_THIS_STAGING_DB_PASSWORD

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.ecommflex.com

BROADCAST_CONNECTION=redis
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=ecommflex_staging

MEMCACHED_HOST=staging-cache.ecommflex.com

REDIS_CLIENT=phpredis
REDIS_HOST=staging-redis.ecommflex.com
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_SCHEME=tls
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=CHANGE_THIS_MAIL_USERNAME
MAIL_PASSWORD=CHANGE_THIS_MAIL_PASSWORD
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME} Staging"

# AWS Configuration for staging
AWS_ACCESS_KEY_ID=CHANGE_THIS_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=CHANGE_THIS_AWS_SECRET_KEY
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=ecommflex-staging-storage
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Multi-tenant configuration
TENANT_DATABASE_PREFIX=tenant_staging_
TENANT_DOMAIN_SUFFIX=.staging.ecommflex.com
TENANT_CACHE_PREFIX=tenant_staging_

# Performance monitoring
TELESCOPE_ENABLED=true
DEBUGBAR_ENABLED=true
SENTRY_LARAVEL_DSN=CHANGE_THIS_SENTRY_DSN

# Feature flags for staging
FEATURE_MULTI_TENANT=true
FEATURE_PAYMENT_GATEWAYS=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_API_RATE_LIMITING=true
FEATURE_BACKUP_SYSTEM=true

# Payment gateway configuration (staging/sandbox)
STRIPE_KEY=pk_test_CHANGE_THIS_STRIPE_TEST_KEY
STRIPE_SECRET=sk_test_CHANGE_THIS_STRIPE_TEST_SECRET
PAYPAL_MODE=sandbox
PAYPAL_CLIENT_ID=CHANGE_THIS_PAYPAL_SANDBOX_CLIENT_ID
PAYPAL_CLIENT_SECRET=CHANGE_THIS_PAYPAL_SANDBOX_SECRET

# SSL/Security configuration
FORCE_HTTPS=true
SECURE_COOKIES=true
CSRF_PROTECTION=true

# Rate limiting
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=60

# Backup configuration
BACKUP_DISK=s3
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30

# Monitoring and alerting
HEALTH_CHECK_URL=https://staging.ecommflex.com/health
UPTIME_MONITOR_ENABLED=true
PERFORMANCE_MONITOR_ENABLED=true

# Search configuration
SCOUT_DRIVER=algolia
ALGOLIA_APP_ID=CHANGE_THIS_ALGOLIA_APP_ID
ALGOLIA_SECRET=CHANGE_THIS_ALGOLIA_SECRET

# CDN configuration
CDN_URL=https://cdn-staging.ecommflex.com
ASSET_URL=https://cdn-staging.ecommflex.com

# Analytics
GOOGLE_ANALYTICS_ID=GA-STAGING-ID
FACEBOOK_PIXEL_ID=FB-STAGING-PIXEL-ID

# Social login (staging)
GOOGLE_CLIENT_ID=CHANGE_THIS_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=CHANGE_THIS_GOOGLE_CLIENT_SECRET
FACEBOOK_CLIENT_ID=CHANGE_THIS_FACEBOOK_CLIENT_ID
FACEBOOK_CLIENT_SECRET=CHANGE_THIS_FACEBOOK_CLIENT_SECRET

# Notification channels
SLACK_WEBHOOK_URL=CHANGE_THIS_SLACK_WEBHOOK_URL
DISCORD_WEBHOOK_URL=CHANGE_THIS_DISCORD_WEBHOOK_URL

# Development tools (enabled in staging)
TELESCOPE_ENABLED=true
HORIZON_ENABLED=true
PULSE_ENABLED=true
