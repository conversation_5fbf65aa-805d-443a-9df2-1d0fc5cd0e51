name: 'Deploy with Backup'
description: 'Performs deployment with automatic backup and rollback capabilities'
author: 'EcommFlex Team'
inputs:
  host:
    description: 'Deployment host'
    required: true
  user:
    description: 'SSH user'
    required: true
  deployment_path:
    description: 'Deployment path on server'
    required: true
  artifact_name:
    description: 'Name of the deployment artifact'
    required: true
  environment:
    description: 'Deployment environment'
    required: true
  commit_sha:
    description: 'Git commit SHA'
    required: true

outputs:
  deployment-successful:
    description: 'Whether deployment was successful'
    value: ${{ steps.deploy.outputs.deployment-successful }}
  backup-created:
    description: 'Whether backup was created'
    value: ${{ steps.backup.outputs.backup-created }}
runs:
  using: 'composite'
  steps:
    - name: Deploy with backup
      shell: bash
      env:
        USER: ${{ inputs.user }}
        HOST: ${{ inputs.host }}
        PATH: ${{ inputs.path }}
        PACKAGE_NAME: ${{ inputs.package_name }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🚀 Starting deployment to $ENVIRONMENT server..."
        
        # Create backup of current deployment
        echo "📦 Creating backup of current deployment..."
        ssh "$USER@$HOST" "
          cd $PATH &&
          if [ -d current ]; then
            timestamp=\$(date +%Y%m%d-%H%M%S)
            cp -r current backup-\$timestamp &&
            echo '✅ Backup created: backup-'\$timestamp
          else
            echo '⚠️ No current deployment found, skipping backup'
          fi
        "
        
        # Upload new deployment
        echo "📤 Uploading deployment package..."
        if ! scp deployment/$PACKAGE_NAME "$USER@$HOST:$PATH/"; then
          echo "❌ Failed to upload deployment package"
          exit 1
        fi
        
        # Extract and setup new deployment
        echo "📂 Extracting deployment package..."
        ssh "$USER@$HOST" "
          cd $PATH &&
          mkdir -p releases/\$(basename $PACKAGE_NAME .tar.gz | sed 's/.*-//') &&
          tar -xzf $PACKAGE_NAME -C releases/\$(basename $PACKAGE_NAME .tar.gz | sed 's/.*-//') &&
          rm $PACKAGE_NAME &&
          echo '✅ Deployment package extracted successfully'
        "
        
        echo "✅ Deployment completed successfully"
