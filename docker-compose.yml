version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ecommflex-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - ecommflex-network
    depends_on:
      - mysql
      - redis
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - DB_DATABASE=ecommflex
      - DB_USERNAME=ecommflex
      - DB_PASSWORD=secret
      - REDIS_HOST=redis

  nginx:
    image: nginx:alpine
    container_name: ecommflex-nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - ecommflex-network
    depends_on:
      - app

  mysql:
    image: mysql:8.0
    container_name: ecommflex-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: ecommflex
      MYSQL_USER: ecommflex
      MYSQL_PASSWORD: secret
      MYSQL_ROOT_PASSWORD: secret
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - mysql-data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - ecommflex-network
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    container_name: ecommflex-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - ecommflex-network
    command: redis-server /usr/local/etc/redis/redis.conf

  mailhog:
    image: mailhog/mailhog
    container_name: ecommflex-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - ecommflex-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: ecommflex-phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: secret
    networks:
      - ecommflex-network
    depends_on:
      - mysql

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ecommflex-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    networks:
      - ecommflex-network
    depends_on:
      - redis

  # Queue worker
  queue:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ecommflex-queue
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - ecommflex-network
    depends_on:
      - mysql
      - redis
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - DB_DATABASE=ecommflex
      - DB_USERNAME=ecommflex
      - DB_PASSWORD=secret
      - REDIS_HOST=redis
    command: php artisan queue:work --verbose --tries=3 --timeout=90

  # Scheduler
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ecommflex-scheduler
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - ecommflex-network
    depends_on:
      - mysql
      - redis
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - DB_DATABASE=ecommflex
      - DB_USERNAME=ecommflex
      - DB_PASSWORD=secret
      - REDIS_HOST=redis
    command: >
      sh -c "while true; do
        php artisan schedule:run --verbose --no-interaction &
        sleep 60
      done"

networks:
  ecommflex-network:
    driver: bridge

volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local
