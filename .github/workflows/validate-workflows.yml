name: Validate Workflows

on:
  pull_request:
    paths:
      - '.github/workflows/**'
      - '.github/actions/**'
  push:
    branches:
      - master
      - staging
    paths:
      - '.github/workflows/**'
      - '.github/actions/**'

jobs:
  validate:
    name: Validate GitHub Actions
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'
      
      - name: Install PyYAML
        run: pip install PyYAML
      
      - name: Validate workflow syntax
        run: |
          echo "🔍 Validating workflow YAML syntax..."
          for file in .github/workflows/*.yml .github/workflows/*.yaml; do
            if [ -f "$file" ]; then
              echo "Validating: $file"
              python -c "import yaml; yaml.safe_load(open('$file'))"
              echo "✅ $file is valid"
            fi
          done
      
      - name: Validate composite actions
        run: |
          echo "🔍 Validating composite action syntax..."
          for file in .github/actions/*/action.yml .github/actions/*/action.yaml; do
            if [ -f "$file" ]; then
              echo "Validating: $file"
              python -c "import yaml; yaml.safe_load(open('$file'))"
              echo "✅ $file is valid"
            fi
          done
      
      - name: Run workflow validation script
        run: |
          chmod +x scripts/validate-workflows.sh
          ./scripts/validate-workflows.sh
