# eCommFlex Deployment Guide

## Prerequisites

### GitHub Secrets Configuration

Before deploying, configure the following secrets in your GitHub repository:

#### Application Secrets
```
APP_KEY=base64:your-laravel-app-key-here
```

#### Database Secrets
```
STAGING_DB_PASSWORD=your-staging-db-password
PRODUCTION_DB_PASSWORD=your-production-db-password
```

#### Server Access Secrets
```
STAGING_SSH_PRIVATE_KEY=your-staging-ssh-private-key
STAGING_HOST=staging.ecommflex.com
STAGING_USER=deploy
STAGING_PATH=/var/www/ecommflex-staging

PRODUCTION_SSH_PRIVATE_KEY=your-production-ssh-private-key
PRODUCTION_HOST=ecommflex.com
PRODUCTION_USER=deploy
PRODUCTION_PATH=/var/www/ecommflex-production
```

#### External Service Secrets
```
SLACK_WEBHOOK_URL=your-slack-webhook-for-notifications
SENTRY_DSN=your-sentry-dsn-for-error-tracking
```

### Server Requirements

#### Minimum System Requirements
- **CPU**: 2 cores (4 cores recommended for production)
- **RAM**: 4GB (8GB recommended for production)
- **Storage**: 50GB SSD (100GB+ for production)
- **OS**: Ubuntu 20.04 LTS or newer

#### Software Requirements
- **PHP**: 8.4 with required extensions
- **Node.js**: 22.x
- **MySQL**: 8.0
- **Redis**: 7.x
- **Nginx**: Latest stable
- **Supervisor**: For queue management

## Server Setup

### 1. Initial Server Configuration

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y software-properties-common curl wget git unzip

# Add PHP repository
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update

# Install PHP and extensions
sudo apt install -y php8.4 php8.4-fpm php8.4-mysql php8.4-redis \
    php8.4-mbstring php8.4-xml php8.4-curl php8.4-zip \
    php8.4-gd php8.4-intl php8.4-bcmath php8.4-soap

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt install -y nodejs

# Install MySQL
sudo apt install -y mysql-server

# Install Redis
sudo apt install -y redis-server

# Install Nginx
sudo apt install -y nginx

# Install Supervisor
sudo apt install -y supervisor

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2. Database Setup

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create databases and users
sudo mysql -u root -p << EOF
CREATE DATABASE ecommflex_staging;
CREATE DATABASE ecommflex_production;

CREATE USER 'ecommflex_staging'@'localhost' IDENTIFIED BY 'your-staging-password';
CREATE USER 'ecommflex_production'@'localhost' IDENTIFIED BY 'your-production-password';

GRANT ALL PRIVILEGES ON ecommflex_staging.* TO 'ecommflex_staging'@'localhost';
GRANT ALL PRIVILEGES ON ecommflex_production.* TO 'ecommflex_production'@'localhost';

FLUSH PRIVILEGES;
EOF
```

### 3. User and Directory Setup

```bash
# Create deployment user
sudo adduser deploy
sudo usermod -aG www-data deploy

# Create application directories
sudo mkdir -p /var/www/ecommflex-staging
sudo mkdir -p /var/www/ecommflex-production
sudo chown -R deploy:www-data /var/www/ecommflex-*

# Setup SSH for deployment user
sudo -u deploy mkdir -p /home/<USER>/.ssh
sudo -u deploy chmod 700 /home/<USER>/.ssh

# Add your public key to authorized_keys
sudo -u deploy nano /home/<USER>/.ssh/authorized_keys
sudo -u deploy chmod 600 /home/<USER>/.ssh/authorized_keys
```

### 4. Nginx Configuration

Create staging configuration:
```bash
sudo nano /etc/nginx/sites-available/ecommflex-staging
```

```nginx
server {
    listen 80;
    server_name staging.ecommflex.com;
    root /var/www/ecommflex-staging/current/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

Create production configuration:
```bash
sudo nano /etc/nginx/sites-available/ecommflex-production
```

```nginx
server {
    listen 80;
    server_name ecommflex.com www.ecommflex.com;
    root /var/www/ecommflex-production/current/public;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

Enable sites:
```bash
sudo ln -s /etc/nginx/sites-available/ecommflex-staging /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/ecommflex-production /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. SSL Certificate Setup

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Get SSL certificates
sudo certbot --nginx -d staging.ecommflex.com
sudo certbot --nginx -d ecommflex.com -d www.ecommflex.com

# Setup auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 6. Supervisor Configuration

Create queue worker configuration:
```bash
sudo nano /etc/supervisor/conf.d/ecommflex-staging-queue.conf
```

```ini
[program:ecommflex-staging-queue]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/ecommflex-staging/current/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=deploy
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/ecommflex-staging/storage/logs/queue.log
stopwaitsecs=3600
```

```bash
sudo nano /etc/supervisor/conf.d/ecommflex-production-queue.conf
```

```ini
[program:ecommflex-production-queue]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/ecommflex-production/current/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=deploy
numprocs=4
redirect_stderr=true
stdout_logfile=/var/www/ecommflex-production/storage/logs/queue.log
stopwaitsecs=3600
```

Update supervisor:
```bash
sudo supervisorctl reread
sudo supervisorctl update
```

## Environment Files

### Staging Environment (`.env.staging`)

Place this file in `/var/www/ecommflex-staging/.env.staging`:

```env
APP_NAME=eCommFlex
APP_ENV=staging
APP_KEY=base64:your-staging-app-key
APP_DEBUG=true
APP_URL=https://staging.ecommflex.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ecommflex_staging
DB_USERNAME=ecommflex_staging
DB_PASSWORD=your-staging-db-password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Add other staging-specific configurations
```

### Production Environment (`.env.production`)

Place this file in `/var/www/ecommflex-production/.env.production`:

```env
APP_NAME=eCommFlex
APP_ENV=production
APP_KEY=base64:your-production-app-key
APP_DEBUG=false
APP_URL=https://ecommflex.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ecommflex_production
DB_USERNAME=ecommflex_production
DB_PASSWORD=your-production-db-password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Add other production-specific configurations
```

## Deployment Process

### Automatic Deployment

The CI/CD pipeline handles automatic deployments:

1. **Staging**: Triggered on push to `staging` branch
2. **Production**: Triggered on push to `master` branch (requires manual approval)

### Manual Deployment

If you need to deploy manually:

```bash
# Clone repository
git clone https://github.com/your-org/ecommflex.com.git
cd ecommflex.com

# Install dependencies
composer install --no-dev --optimize-autoloader
npm ci && npm run build

# Setup environment
cp .env.staging .env  # or .env.production
php artisan key:generate

# Run migrations
php artisan migrate --force

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set permissions
sudo chown -R deploy:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache
```

## Monitoring Setup

### Health Checks

The application provides several health check endpoints:

- `GET /health` - Basic health check
- `GET /health/comprehensive` - Full system health
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

### Log Monitoring

```bash
# Application logs
tail -f /var/www/ecommflex-production/storage/logs/laravel.log

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# PHP-FPM logs
tail -f /var/log/php8.4-fpm.log

# Queue logs
tail -f /var/www/ecommflex-production/storage/logs/queue.log
```

## Backup Strategy

### Database Backups

Create backup script:
```bash
sudo nano /usr/local/bin/backup-ecommflex.sh
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/ecommflex"

mkdir -p $BACKUP_DIR

# Backup production database
mysqldump -u ecommflex_production -p ecommflex_production > $BACKUP_DIR/production_$DATE.sql

# Backup staging database
mysqldump -u ecommflex_staging -p ecommflex_staging > $BACKUP_DIR/staging_$DATE.sql

# Compress backups
gzip $BACKUP_DIR/production_$DATE.sql
gzip $BACKUP_DIR/staging_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

```bash
sudo chmod +x /usr/local/bin/backup-ecommflex.sh

# Add to crontab
sudo crontab -e
# Add: 0 2 * * * /usr/local/bin/backup-ecommflex.sh
```

### File Backups

```bash
# Backup application files
tar -czf /var/backups/ecommflex/app_backup_$(date +%Y%m%d).tar.gz \
    /var/www/ecommflex-production \
    --exclude=/var/www/ecommflex-production/storage/logs \
    --exclude=/var/www/ecommflex-production/node_modules
```

## Troubleshooting

### Common Issues

1. **Permission Issues**
   ```bash
   sudo chown -R deploy:www-data /var/www/ecommflex-*
   sudo chmod -R 775 storage bootstrap/cache
   ```

2. **Queue Not Processing**
   ```bash
   sudo supervisorctl restart ecommflex-production-queue:*
   ```

3. **Database Connection Issues**
   ```bash
   # Check MySQL status
   sudo systemctl status mysql
   
   # Test connection
   mysql -u ecommflex_production -p ecommflex_production
   ```

4. **Redis Connection Issues**
   ```bash
   # Check Redis status
   sudo systemctl status redis
   
   # Test connection
   redis-cli ping
   ```

### Performance Optimization

1. **Enable OPcache**
   ```bash
   sudo nano /etc/php/8.4/fpm/conf.d/10-opcache.ini
   ```

2. **Optimize MySQL**
   ```bash
   sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
   ```

3. **Tune Nginx**
   ```bash
   sudo nano /etc/nginx/nginx.conf
   ```

This deployment guide ensures a robust, scalable deployment of the eCommFlex platform with proper security, monitoring, and backup procedures.
