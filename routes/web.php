<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\HealthController;

// Health check routes (no authentication required)
Route::get('/health', [HealthController::class, 'basic'])->name('health.basic');
Route::get('/health/comprehensive', [HealthController::class, 'comprehensive'])->name('health.comprehensive');
Route::get('/health/ready', [HealthController::class, 'ready'])->name('health.ready');
Route::get('/health/live', [HealthController::class, 'live'])->name('health.live');
Route::get('/health/tenant', [HealthController::class, 'tenant'])->name('health.tenant');

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
