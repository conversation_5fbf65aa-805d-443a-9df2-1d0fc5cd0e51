name: Comprehensive Testing Suite

on:
  push:
    branches:
      - master
      - staging
      - firozanam
      - mir-ratul
  pull_request:
    branches:
      - master
      - staging
      - firozanam
      - mir-ratul

env:
  DB_CONNECTION: sqlite
  DB_DATABASE: ':memory:'
  CACHE_DRIVER: array
  SESSION_DRIVER: array
  QUEUE_DRIVER: sync

jobs:
  backend-tests:
    name: Backend Tests (PHP ${{ matrix.php-version }})
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php-version: ['8.2', '8.4']

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: ecommflex_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
          coverage: xdebug
          tools: composer:v2

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: ~/.composer/cache/files
          key: composer-${{ matrix.php-version }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            composer-${{ matrix.php-version }}-
            composer-

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Create SQLite database
        run: touch database/database.sqlite

      - name: Copy environment file
        run: cp .env.example .env

      - name: Generate application key
        run: php artisan key:generate

      - name: Run database migrations (SQLite)
        run: php artisan migrate --force

      - name: Run PHPUnit tests with coverage
        run: vendor/bin/phpunit --coverage-clover=coverage.xml --coverage-text --coverage-html=coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          flags: backend
          name: backend-coverage-${{ matrix.php-version }}

  mysql-integration-tests:
    name: MySQL Integration Tests
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: ecommflex_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
          tools: composer:v2

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Copy environment file
        run: cp .env.example .env

      - name: Configure MySQL environment
        run: |
          echo "DB_CONNECTION=mysql" >> .env
          echo "DB_HOST=127.0.0.1" >> .env
          echo "DB_PORT=3306" >> .env
          echo "DB_DATABASE=ecommflex_test" >> .env
          echo "DB_USERNAME=root" >> .env
          echo "DB_PASSWORD=password" >> .env

      - name: Generate application key
        run: php artisan key:generate

      - name: Run database migrations (MySQL)
        run: php artisan migrate --force

      - name: Run MySQL-specific tests
        run: vendor/bin/phpunit --group=mysql

  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install Node dependencies
        run: npm ci

      - name: Run TypeScript type checking
        run: npm run types

      - name: Build frontend assets
        run: npm run build

      # Note: Add frontend tests when test framework is set up
      # - name: Run frontend unit tests
      #   run: npm run test

      # - name: Run E2E tests
      #   run: npm run test:e2e
