[PHP]
; PHP Configuration for eCommFlex

; Error reporting
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Memory and execution limits
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000

; File uploads
file_uploads = On
upload_max_filesize = 100M
max_file_uploads = 20
post_max_size = 100M

; Session configuration
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Lax"

; Security settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Date and timezone
date.timezone = UTC

; Realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; OPcache settings (will be overridden by opcache.ini)
opcache.enable = 1
opcache.enable_cli = 0
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1

; MySQL settings
mysqli.default_host = mysql
mysqli.default_user = ecommflex
mysqli.default_pw = secret
mysqli.default_port = 3306

; Performance tuning
output_buffering = 4096
implicit_flush = Off
unserialize_callback_func =
serialize_precision = -1

; Security headers
fastcgi.logging = 1

; Disable dangerous functions
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source
