name: Code Quality & Linting

on:
  push:
    branches:
      - master
      - staging
      - firozanam
      - mir-ratul
  pull_request:
    branches:
      - master
      - staging
      - firozanam
      - mir-ratul

permissions:
  contents: read
  security-events: write

jobs:
  php-quality:
    name: PHP Code Quality
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-version: ['8.2', '8.4']

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
          coverage: xdebug

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: ~/.composer/cache/files
          key: composer-${{ matrix.php-version }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            composer-${{ matrix.php-version }}-
            composer-

      - name: Install Composer dependencies
        run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist --optimize-autoloader

      - name: Run PHP Pint (Code Style)
        run: vendor/bin/pint --test

      - name: Run PHP Static Analysis (PHPStan)
        run: |
          if [ ! -f phpstan.neon ]; then
            echo "Creating basic PHPStan configuration..."
            cat > phpstan.neon << 'EOF'
          parameters:
              level: 5
              paths:
                  - app
              excludePaths:
                  - app/Console/Kernel.php
          EOF
          fi
          composer require --dev phpstan/phpstan --no-interaction
          vendor/bin/phpstan analyse --memory-limit=2G

  frontend-quality:
    name: Frontend Code Quality
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install Node dependencies
        run: npm ci

      - name: Check TypeScript types
        run: npm run types

      - name: Run ESLint
        run: npm run lint

      - name: Check Prettier formatting
        run: npm run format:check

      - name: Build frontend assets
        run: npm run build

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run PHP Security Checker
        run: |
          curl -H "Accept: text/plain" https://security.symfony.com/check_lock -F lock=@composer.lock

      - name: Run npm audit
        run: npm audit --audit-level=moderate

      - name: Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified
