<?php

namespace Tests\Unit;

use Tests\TestCase;
use Symfony\Component\Yaml\Yaml;
use Symfony\Component\Yaml\Exception\ParseException;

class WorkflowValidationTest extends TestCase
{
    private array $requiredWorkflowFiles = [
        '.github/workflows/deploy-staging.yml',
        '.github/workflows/deploy-production.yml',
        '.github/workflows/ci.yml',
        '.github/workflows/tests.yml',
    ];

    private array $requiredCompositeActions = [
        '.github/actions/validate-deployment-secrets/action.yml',
        '.github/actions/setup-deployment-ssh/action.yml',
        '.github/actions/deploy-with-backup/action.yml',
    ];

    private array $stagingSecrets = [
        'STAGING_SSH_PRIVATE_KEY',
        'STAGING_HOST',
        'STAGING_USER',
        'STAGING_PATH',
    ];

    private array $productionSecrets = [
        'PRODUCTION_SSH_PRIVATE_KEY',
        'PRODUCTION_HOST',
        'PRODUCTION_USER',
        'PRODUCTION_PATH',
    ];

    public function test_workflow_files_exist(): void
    {
        foreach ($this->requiredWorkflowFiles as $file) {
            $this->assertFileExists(
                base_path($file),
                "Required workflow file missing: {$file}"
            );
        }
    }

    public function test_workflow_yaml_syntax_is_valid(): void
    {
        foreach ($this->requiredWorkflowFiles as $file) {
            $filePath = base_path($file);
            
            if (!file_exists($filePath)) {
                $this->markTestSkipped("File does not exist: {$file}");
            }

            try {
                $content = file_get_contents($filePath);
                Yaml::parse($content);
                $this->assertTrue(true, "YAML syntax valid for: {$file}");
            } catch (ParseException $e) {
                $this->fail("YAML syntax invalid for {$file}: " . $e->getMessage());
            }
        }
    }

    public function test_composite_actions_exist(): void
    {
        foreach ($this->requiredCompositeActions as $action) {
            $this->assertFileExists(
                base_path($action),
                "Required composite action missing: {$action}"
            );
        }
    }

    public function test_composite_actions_yaml_syntax(): void
    {
        foreach ($this->requiredCompositeActions as $action) {
            $filePath = base_path($action);
            
            if (!file_exists($filePath)) {
                $this->markTestSkipped("File does not exist: {$action}");
            }

            try {
                $content = file_get_contents($filePath);
                Yaml::parse($content);
                $this->assertTrue(true, "YAML syntax valid for: {$action}");
            } catch (ParseException $e) {
                $this->fail("YAML syntax invalid for {$action}: " . $e->getMessage());
            }
        }
    }

    public function test_staging_workflow_has_secret_validation(): void
    {
        $filePath = base_path('.github/workflows/deploy-staging.yml');
        
        if (!file_exists($filePath)) {
            $this->markTestSkipped('Staging workflow file does not exist');
        }

        $content = file_get_contents($filePath);
        
        $this->assertStringContainsString(
            'validate-secrets:',
            $content,
            'Staging workflow missing secret validation job'
        );
    }

    public function test_production_workflow_has_secret_validation(): void
    {
        $filePath = base_path('.github/workflows/deploy-production.yml');
        
        if (!file_exists($filePath)) {
            $this->markTestSkipped('Production workflow file does not exist');
        }

        $content = file_get_contents($filePath);
        
        $this->assertStringContainsString(
            'validate-secrets:',
            $content,
            'Production workflow missing secret validation job'
        );
    }

    public function test_staging_secrets_are_documented(): void
    {
        $filePath = base_path('.github/workflows/deploy-staging.yml');
        
        if (!file_exists($filePath)) {
            $this->markTestSkipped('Staging workflow file does not exist');
        }

        $content = file_get_contents($filePath);
        
        foreach ($this->stagingSecrets as $secret) {
            $this->assertStringContainsString(
                $secret,
                $content,
                "Staging secret not documented: {$secret}"
            );
        }
    }

    public function test_production_secrets_are_documented(): void
    {
        $filePath = base_path('.github/workflows/deploy-production.yml');
        
        if (!file_exists($filePath)) {
            $this->markTestSkipped('Production workflow file does not exist');
        }

        $content = file_get_contents($filePath);
        
        foreach ($this->productionSecrets as $secret) {
            $this->assertStringContainsString(
                $secret,
                $content,
                "Production secret not documented: {$secret}"
            );
        }
    }

    public function test_workflows_have_proper_job_dependencies(): void
    {
        $deploymentWorkflows = [
            '.github/workflows/deploy-staging.yml',
            '.github/workflows/deploy-production.yml',
        ];

        foreach ($deploymentWorkflows as $workflow) {
            $filePath = base_path($workflow);
            
            if (!file_exists($filePath)) {
                $this->markTestSkipped("Workflow file does not exist: {$workflow}");
            }

            $content = file_get_contents($filePath);
            $needsCount = substr_count($content, 'needs:');
            
            $this->assertGreaterThanOrEqual(
                3,
                $needsCount,
                "Insufficient job dependencies in {$workflow} (found: {$needsCount})"
            );
        }
    }

    public function test_workflows_have_environment_configuration(): void
    {
        // Test staging environment
        $stagingPath = base_path('.github/workflows/deploy-staging.yml');
        if (file_exists($stagingPath)) {
            $content = file_get_contents($stagingPath);
            $this->assertStringContainsString('environment:', $content);
            $this->assertStringContainsString('name: staging', $content);
        }

        // Test production environment
        $productionPath = base_path('.github/workflows/deploy-production.yml');
        if (file_exists($productionPath)) {
            $content = file_get_contents($productionPath);
            $this->assertStringContainsString('environment:', $content);
            $this->assertStringContainsString('name: production', $content);
        }
    }

    public function test_validation_script_exists_and_is_executable(): void
    {
        $scriptPath = base_path('scripts/validate-workflows.sh');
        
        $this->assertFileExists($scriptPath, 'Validation script missing');
        $this->assertTrue(is_executable($scriptPath), 'Validation script is not executable');
    }

    public function test_workflow_structure_integrity(): void
    {
        $deploymentWorkflows = [
            '.github/workflows/deploy-staging.yml',
            '.github/workflows/deploy-production.yml',
        ];

        foreach ($deploymentWorkflows as $workflow) {
            $filePath = base_path($workflow);
            
            if (!file_exists($filePath)) {
                continue;
            }

            try {
                $content = file_get_contents($filePath);
                $parsed = Yaml::parse($content);
                
                // Check basic structure
                $this->assertArrayHasKey('name', $parsed, "Missing 'name' in {$workflow}");
                $this->assertArrayHasKey('on', $parsed, "Missing 'on' in {$workflow}");
                $this->assertArrayHasKey('jobs', $parsed, "Missing 'jobs' in {$workflow}");
                
                // Check for validate-secrets job
                $this->assertArrayHasKey('validate-secrets', $parsed['jobs'], 
                    "Missing 'validate-secrets' job in {$workflow}");
                
                // Check job dependencies include validate-secrets
                foreach ($parsed['jobs'] as $jobName => $job) {
                    if ($jobName !== 'validate-secrets' && isset($job['needs'])) {
                        $needs = is_array($job['needs']) ? $job['needs'] : [$job['needs']];
                        if (in_array('validate-secrets', $needs) || 
                            strpos($jobName, 'deploy') !== false || 
                            strpos($jobName, 'build') !== false) {
                            // Deployment-related jobs should depend on validate-secrets
                            $this->assertContains('validate-secrets', $needs, 
                                "Job '{$jobName}' should depend on 'validate-secrets' in {$workflow}");
                        }
                    }
                }
                
            } catch (ParseException $e) {
                $this->fail("Failed to parse workflow {$workflow}: " . $e->getMessage());
            }
        }
    }

    public function test_composite_action_structure(): void
    {
        foreach ($this->requiredCompositeActions as $action) {
            $filePath = base_path($action);
            
            if (!file_exists($filePath)) {
                continue;
            }

            try {
                $content = file_get_contents($filePath);
                $parsed = Yaml::parse($content);
                
                // Check basic structure
                $this->assertArrayHasKey('name', $parsed, "Missing 'name' in {$action}");
                $this->assertArrayHasKey('description', $parsed, "Missing 'description' in {$action}");
                $this->assertArrayHasKey('runs', $parsed, "Missing 'runs' in {$action}");
                
                // Check runs configuration
                $this->assertEquals('composite', $parsed['runs']['using'], 
                    "Action should use 'composite' in {$action}");
                $this->assertArrayHasKey('steps', $parsed['runs'], 
                    "Missing 'steps' in runs configuration for {$action}");
                
            } catch (ParseException $e) {
                $this->fail("Failed to parse composite action {$action}: " . $e->getMessage());
            }
        }
    }
}
