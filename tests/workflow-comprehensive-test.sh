#!/bin/bash

# Comprehensive GitHub Actions Workflow Testing Suite
# Tests all aspects of the deployment workflows including error scenarios

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

echo -e "${CYAN}🧪 Comprehensive GitHub Actions Workflow Test Suite${NC}"
echo "============================================================"
echo -e "${BLUE}Testing all workflow components and error scenarios...${NC}"
echo ""

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TESTS_TOTAL++))
    echo -e "${BLUE}🧪 Testing: $test_name${NC}"
    
    if eval "$test_command" &>/dev/null; then
        echo -e "${GREEN}✅ PASS: $test_name${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL: $test_name${NC}"
        ((TESTS_FAILED++))
    fi
}

# Function to test YAML syntax
test_yaml_syntax() {
    echo -e "${CYAN}📋 Testing YAML Syntax${NC}"
    echo "------------------------------------------------------------"
    
    # Test workflow files
    for file in .github/workflows/*.yml .github/workflows/*.yaml; do
        if [ -f "$file" ]; then
            run_test "YAML syntax: $(basename $file)" "python3 -c \"import yaml; yaml.safe_load(open('$file'))\""
        fi
    done
    
    # Test composite actions
    for file in .github/actions/*/action.yml .github/actions/*/action.yaml; do
        if [ -f "$file" ]; then
            run_test "Action syntax: $(basename $(dirname $file))" "python3 -c \"import yaml; yaml.safe_load(open('$file'))\""
        fi
    done
}

# Function to test composite actions structure
test_composite_actions() {
    echo ""
    echo -e "${CYAN}🔧 Testing Composite Actions${NC}"
    echo "------------------------------------------------------------"
    
    local required_actions=(
        "validate-deployment-secrets"
        "setup-deployment-ssh"
        "deploy-with-backup"
    )
    
    for action in "${required_actions[@]}"; do
        run_test "Composite action exists: $action" "[ -f .github/actions/$action/action.yml ]"
        
        if [ -f ".github/actions/$action/action.yml" ]; then
            run_test "Action has required metadata: $action" "grep -q 'name:' .github/actions/$action/action.yml && grep -q 'description:' .github/actions/$action/action.yml"
            run_test "Action has inputs: $action" "grep -q 'inputs:' .github/actions/$action/action.yml"
            run_test "Action has steps: $action" "grep -q 'steps:' .github/actions/$action/action.yml"
        fi
    done
}

# Function to test workflow structure
test_workflow_structure() {
    echo ""
    echo -e "${CYAN}📋 Testing Workflow Structure${NC}"
    echo "------------------------------------------------------------"
    
    local workflows=(
        "deploy-staging.yml"
        "deploy-production.yml"
    )
    
    for workflow in "${workflows[@]}"; do
        local file=".github/workflows/$workflow"
        if [ -f "$file" ]; then
            run_test "Workflow has name: $workflow" "grep -q 'name:' $file"
            run_test "Workflow has triggers: $workflow" "grep -q 'on:' $file"
            run_test "Workflow has jobs: $workflow" "grep -q 'jobs:' $file"
            run_test "Workflow has secret validation: $workflow" "grep -q 'validate-secrets' $file"
            run_test "Workflow has environment config: $workflow" "grep -q 'environment:' $file"
        fi
    done
}

# Function to test secret validation patterns
test_secret_validation() {
    echo ""
    echo -e "${CYAN}🔐 Testing Secret Validation Patterns${NC}"
    echo "------------------------------------------------------------"
    
    # Test staging secrets
    local staging_secrets=("STAGING_SSH_PRIVATE_KEY" "STAGING_HOST" "STAGING_USER" "STAGING_PATH")
    for secret in "${staging_secrets[@]}"; do
        run_test "Staging secret referenced: $secret" "grep -q '$secret' .github/workflows/deploy-staging.yml"
    done
    
    # Test production secrets
    local production_secrets=("PRODUCTION_SSH_PRIVATE_KEY" "PRODUCTION_HOST" "PRODUCTION_USER" "PRODUCTION_PATH")
    for secret in "${production_secrets[@]}"; do
        run_test "Production secret referenced: $secret" "grep -q '$secret' .github/workflows/deploy-production.yml"
    done
}

# Function to test error handling patterns
test_error_handling() {
    echo ""
    echo -e "${CYAN}🚨 Testing Error Handling Patterns${NC}"
    echo "------------------------------------------------------------"
    
    # Test conditional execution
    run_test "Staging workflow has conditional execution" "grep -q 'if:.*secrets-available' .github/workflows/deploy-staging.yml"
    run_test "Production workflow has conditional execution" "grep -q 'if:.*secrets-available' .github/workflows/deploy-production.yml"
    
    # Test job dependencies
    run_test "Staging workflow has job dependencies" "grep -q 'needs:' .github/workflows/deploy-staging.yml"
    run_test "Production workflow has job dependencies" "grep -q 'needs:' .github/workflows/deploy-production.yml"
    
    # Test rollback capabilities
    run_test "Staging workflow has rollback job" "grep -q 'rollback' .github/workflows/deploy-staging.yml"
    run_test "Production workflow has rollback job" "grep -q 'rollback' .github/workflows/deploy-production.yml"
}

# Function to test documentation
test_documentation() {
    echo ""
    echo -e "${CYAN}📚 Testing Documentation${NC}"
    echo "------------------------------------------------------------"
    
    local required_docs=(
        "docs/templates/github-environments-setup.md"
        "docs/GitHub-Actions-Issues-Resolution.md"
        "scripts/validate-workflows.sh"
        "scripts/debug-workflows.sh"
        "scripts/fix-workflow-issues.sh"
    )
    
    for doc in "${required_docs[@]}"; do
        run_test "Documentation exists: $(basename $doc)" "[ -f $doc ]"
    done
}

# Function to test script executability
test_script_executability() {
    echo ""
    echo -e "${CYAN}🔧 Testing Script Executability${NC}"
    echo "------------------------------------------------------------"
    
    local scripts=(
        "scripts/validate-workflows.sh"
        "scripts/debug-workflows.sh"
        "scripts/fix-workflow-issues.sh"
    )
    
    for script in "${scripts[@]}"; do
        run_test "Script is executable: $(basename $script)" "[ -x $script ]"
    done
}

# Function to test environment configuration
test_environment_config() {
    echo ""
    echo -e "${CYAN}🌍 Testing Environment Configuration${NC}"
    echo "------------------------------------------------------------"
    
    # Test staging environment
    run_test "Staging environment configured" "grep -q 'name: staging' .github/workflows/deploy-staging.yml"
    
    # Test production environments
    run_test "Production environment configured" "grep -q 'name: production' .github/workflows/deploy-production.yml"
    run_test "Approval environment configured" "grep -q 'name: approval' .github/workflows/deploy-production.yml"
}

# Function to simulate error scenarios
test_error_scenarios() {
    echo ""
    echo -e "${CYAN}💥 Testing Error Scenarios${NC}"
    echo "------------------------------------------------------------"
    
    # Create temporary test files for error scenarios
    local temp_dir=$(mktemp -d)
    
    # Test invalid YAML
    echo "invalid: yaml: content" > "$temp_dir/invalid.yml"
    run_test "Invalid YAML detection" "! python3 -c \"import yaml; yaml.safe_load(open('$temp_dir/invalid.yml'))\" 2>/dev/null"
    
    # Test missing required fields
    echo "name: test" > "$temp_dir/incomplete.yml"
    run_test "Incomplete workflow detection" "! grep -q 'jobs:' $temp_dir/incomplete.yml"
    
    # Cleanup
    rm -rf "$temp_dir"
}

# Main test execution
main() {
    echo -e "${BLUE}🚀 Starting comprehensive workflow testing...${NC}"
    echo ""
    
    # Check prerequisites
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 is required for YAML validation${NC}"
        exit 1
    fi
    
    if ! python3 -c "import yaml" 2>/dev/null; then
        echo -e "${YELLOW}⚠️ Installing PyYAML for testing...${NC}"
        pip3 install PyYAML || {
            echo -e "${RED}❌ Failed to install PyYAML${NC}"
            exit 1
        }
    fi
    
    # Run all test suites
    test_yaml_syntax
    test_composite_actions
    test_workflow_structure
    test_secret_validation
    test_error_handling
    test_documentation
    test_script_executability
    test_environment_config
    test_error_scenarios
    
    # Print summary
    echo ""
    echo "============================================================"
    echo -e "${CYAN}📊 Test Summary${NC}"
    echo "============================================================"
    echo -e "Total Tests: ${BLUE}$TESTS_TOTAL${NC}"
    echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Failed: ${RED}$TESTS_FAILED${NC}"
    echo -e "Success Rate: ${CYAN}$(( TESTS_PASSED * 100 / TESTS_TOTAL ))%${NC}"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}✅ All tests passed! Workflows are ready for production.${NC}"
        exit 0
    else
        echo -e "${RED}❌ $TESTS_FAILED tests failed. Please review and fix issues.${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
