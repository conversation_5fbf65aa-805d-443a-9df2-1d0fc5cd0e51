#!/bin/bash

# Simple workflow validation test
echo "🧪 Testing workflow validation..."

# Test 1: Check files exist
echo "Checking workflow files..."
for file in .github/workflows/deploy-staging.yml .github/workflows/deploy-production.yml; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file missing"
    fi
done

# Test 2: Check YAML syntax
echo "Checking YAML syntax..."
for file in .github/workflows/deploy-staging.yml .github/workflows/deploy-production.yml; do
    if [ -f "$file" ]; then
        if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            echo "✅ $file YAML syntax valid"
        else
            echo "❌ $file YAML syntax invalid"
        fi
    fi
done

# Test 3: Check for secret validation jobs
echo "Checking secret validation jobs..."
for file in .github/workflows/deploy-staging.yml .github/workflows/deploy-production.yml; do
    if [ -f "$file" ]; then
        if grep -q "validate-secrets:" "$file"; then
            echo "✅ $file has secret validation job"
        else
            echo "❌ $file missing secret validation job"
        fi
    fi
done

# Test 4: Check composite actions
echo "Checking composite actions..."
for action in .github/actions/validate-deployment-secrets/action.yml .github/actions/setup-deployment-ssh/action.yml .github/actions/deploy-with-backup/action.yml; do
    if [ -f "$action" ]; then
        echo "✅ $action exists"
    else
        echo "❌ $action missing"
    fi
done

echo "✅ Simple workflow validation completed"
