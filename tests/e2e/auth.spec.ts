import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('user can register successfully', async ({ page }) => {
    // Navigate to registration page
    await page.click('text=Register');
    
    // Fill registration form
    await page.fill('[name="name"]', 'Test User');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.fill('[name="password_confirmation"]', 'password123');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Verify successful registration
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text=Welcome, Test User')).toBeVisible();
  });

  test('user can login successfully', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Login');
    
    // Fill login form
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Verify successful login
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('user cannot login with invalid credentials', async ({ page }) => {
    // Navigate to login page
    await page.click('text=Login');
    
    // Fill login form with invalid credentials
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'wrongpassword');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Verify error message
    await expect(page.locator('text=Invalid credentials')).toBeVisible();
    await expect(page).toHaveURL('/login');
  });

  test('user can logout successfully', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Verify logged in
    await expect(page).toHaveURL('/dashboard');
    
    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('text=Logout');
    
    // Verify logged out
    await expect(page).toHaveURL('/');
    await expect(page.locator('text=Login')).toBeVisible();
  });

  test('password reset flow works correctly', async ({ page }) => {
    // Navigate to forgot password page
    await page.goto('/login');
    await page.click('text=Forgot Password?');
    
    // Fill email
    await page.fill('[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // Verify success message
    await expect(page.locator('text=Password reset link sent')).toBeVisible();
  });

  test('form validation works correctly', async ({ page }) => {
    // Navigate to registration page
    await page.goto('/register');
    
    // Submit empty form
    await page.click('button[type="submit"]');
    
    // Verify validation errors
    await expect(page.locator('text=The name field is required')).toBeVisible();
    await expect(page.locator('text=The email field is required')).toBeVisible();
    await expect(page.locator('text=The password field is required')).toBeVisible();
  });

  test('email validation works correctly', async ({ page }) => {
    await page.goto('/register');
    
    // Fill invalid email
    await page.fill('[name="name"]', 'Test User');
    await page.fill('[name="email"]', 'invalid-email');
    await page.fill('[name="password"]', 'password123');
    await page.fill('[name="password_confirmation"]', 'password123');
    
    await page.click('button[type="submit"]');
    
    // Verify email validation error
    await expect(page.locator('text=Please enter a valid email address')).toBeVisible();
  });

  test('password confirmation validation works', async ({ page }) => {
    await page.goto('/register');
    
    // Fill mismatched passwords
    await page.fill('[name="name"]', 'Test User');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.fill('[name="password_confirmation"]', 'different123');
    
    await page.click('button[type="submit"]');
    
    // Verify password confirmation error
    await expect(page.locator('text=Passwords do not match')).toBeVisible();
  });

  test('remember me functionality works', async ({ page }) => {
    await page.goto('/login');
    
    // Login with remember me checked
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.check('[name="remember"]');
    await page.click('button[type="submit"]');
    
    // Verify successful login
    await expect(page).toHaveURL('/dashboard');
    
    // Close browser and reopen (simulate browser restart)
    await page.context().close();
    const newContext = await page.context().browser()?.newContext();
    const newPage = await newContext?.newPage();
    
    if (newPage) {
      await newPage.goto('/dashboard');
      // Should still be logged in due to remember me
      await expect(newPage).toHaveURL('/dashboard');
    }
  });

  test('session timeout works correctly', async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    
    // Simulate session expiry by clearing cookies
    await page.context().clearCookies();
    
    // Try to access protected page
    await page.goto('/dashboard');
    
    // Should be redirected to login
    await expect(page).toHaveURL('/login');
  });
});
