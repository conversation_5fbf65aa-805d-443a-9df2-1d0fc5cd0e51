<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;

class HealthController extends Controller
{
    /**
     * Basic health check endpoint
     */
    public function basic(): JsonResponse
    {
        return response()->json([
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'service' => 'eCommFlex',
            'version' => config('app.version', '1.0.0'),
        ]);
    }

    /**
     * Comprehensive health check with all dependencies
     */
    public function comprehensive(): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'redis' => $this->checkRedis(),
            'storage' => $this->checkStorage(),
            'queue' => $this->checkQueue(),
            'memory' => $this->checkMemory(),
            'disk' => $this->checkDisk(),
        ];

        $overallStatus = collect($checks)->every(fn($check) => $check['status'] === 'healthy') 
            ? 'healthy' 
            : 'unhealthy';

        return response()->json([
            'status' => $overallStatus,
            'timestamp' => now()->toISOString(),
            'service' => 'eCommFlex',
            'version' => config('app.version', '1.0.0'),
            'checks' => $checks,
            'uptime' => $this->getUptime(),
        ], $overallStatus === 'healthy' ? 200 : 503);
    }

    /**
     * Readiness check for Kubernetes/Docker
     */
    public function ready(): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
        ];

        $ready = collect($checks)->every(fn($check) => $check['status'] === 'healthy');

        return response()->json([
            'ready' => $ready,
            'checks' => $checks,
        ], $ready ? 200 : 503);
    }

    /**
     * Liveness check for Kubernetes/Docker
     */
    public function live(): JsonResponse
    {
        // Basic liveness check - just verify the application is running
        return response()->json([
            'alive' => true,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Multi-tenant health check
     */
    public function tenant(Request $request): JsonResponse
    {
        $tenant = $request->header('X-Tenant') ?? 'default';
        
        $checks = [
            'tenant_database' => $this->checkTenantDatabase($tenant),
            'tenant_cache' => $this->checkTenantCache($tenant),
            'tenant_storage' => $this->checkTenantStorage($tenant),
        ];

        $overallStatus = collect($checks)->every(fn($check) => $check['status'] === 'healthy') 
            ? 'healthy' 
            : 'unhealthy';

        return response()->json([
            'status' => $overallStatus,
            'tenant' => $tenant,
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
        ], $overallStatus === 'healthy' ? 200 : 503);
    }

    /**
     * Check database connectivity
     */
    private function checkDatabase(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'connection' => DB::getDefaultConnection(),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache connectivity
     */
    private function checkCache(): array
    {
        try {
            $start = microtime(true);
            $key = 'health_check_' . time();
            Cache::put($key, 'test', 10);
            $value = Cache::get($key);
            Cache::forget($key);
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => $value === 'test' ? 'healthy' : 'unhealthy',
                'response_time_ms' => $responseTime,
                'driver' => config('cache.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check Redis connectivity
     */
    private function checkRedis(): array
    {
        try {
            $start = microtime(true);
            Redis::ping();
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage accessibility
     */
    private function checkStorage(): array
    {
        try {
            $start = microtime(true);
            $testFile = 'health_check_' . time() . '.txt';
            Storage::put($testFile, 'test');
            $content = Storage::get($testFile);
            Storage::delete($testFile);
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => $content === 'test' ? 'healthy' : 'unhealthy',
                'response_time_ms' => $responseTime,
                'disk' => config('filesystems.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue system
     */
    private function checkQueue(): array
    {
        try {
            $queueSize = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            $status = 'healthy';
            if ($queueSize > 1000) {
                $status = 'warning';
            }
            if ($failedJobs > 100) {
                $status = 'unhealthy';
            }

            return [
                'status' => $status,
                'queue_size' => $queueSize,
                'failed_jobs' => $failedJobs,
                'connection' => config('queue.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check memory usage
     */
    private function checkMemory(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseBytes(ini_get('memory_limit'));
        $memoryPercent = round(($memoryUsage / $memoryLimit) * 100, 2);

        $status = 'healthy';
        if ($memoryPercent > 80) {
            $status = 'warning';
        }
        if ($memoryPercent > 95) {
            $status = 'unhealthy';
        }

        return [
            'status' => $status,
            'usage_bytes' => $memoryUsage,
            'usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'usage_percent' => $memoryPercent,
        ];
    }

    /**
     * Check disk usage
     */
    private function checkDisk(): array
    {
        $path = storage_path();
        $totalBytes = disk_total_space($path);
        $freeBytes = disk_free_space($path);
        $usedBytes = $totalBytes - $freeBytes;
        $usedPercent = round(($usedBytes / $totalBytes) * 100, 2);

        $status = 'healthy';
        if ($usedPercent > 80) {
            $status = 'warning';
        }
        if ($usedPercent > 95) {
            $status = 'unhealthy';
        }

        return [
            'status' => $status,
            'total_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
            'used_gb' => round($usedBytes / 1024 / 1024 / 1024, 2),
            'free_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
            'used_percent' => $usedPercent,
        ];
    }

    /**
     * Check tenant-specific database
     */
    private function checkTenantDatabase(string $tenant): array
    {
        try {
            $connection = "tenant_{$tenant}";
            $start = microtime(true);
            DB::connection($connection)->select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'connection' => $connection,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check tenant-specific cache
     */
    private function checkTenantCache(string $tenant): array
    {
        try {
            $start = microtime(true);
            $key = "tenant_{$tenant}_health_check_" . time();
            Cache::put($key, 'test', 10);
            $value = Cache::get($key);
            Cache::forget($key);
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => $value === 'test' ? 'healthy' : 'unhealthy',
                'response_time_ms' => $responseTime,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check tenant-specific storage
     */
    private function checkTenantStorage(string $tenant): array
    {
        try {
            $start = microtime(true);
            $testFile = "tenant_{$tenant}/health_check_" . time() . '.txt';
            Storage::put($testFile, 'test');
            $content = Storage::get($testFile);
            Storage::delete($testFile);
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => $content === 'test' ? 'healthy' : 'unhealthy',
                'response_time_ms' => $responseTime,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get application uptime
     */
    private function getUptime(): array
    {
        $uptimeFile = storage_path('app/uptime.txt');
        
        if (!file_exists($uptimeFile)) {
            file_put_contents($uptimeFile, time());
        }
        
        $startTime = (int) file_get_contents($uptimeFile);
        $uptime = time() - $startTime;
        
        return [
            'seconds' => $uptime,
            'human' => $this->formatUptime($uptime),
        ];
    }

    /**
     * Format uptime in human readable format
     */
    private function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        return sprintf('%dd %dh %dm %ds', $days, $hours, $minutes, $seconds);
    }

    /**
     * Parse memory limit string to bytes
     */
    private function parseBytes(string $size): int
    {
        $unit = strtolower(substr($size, -1));
        $value = (int) substr($size, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $size;
        }
    }
}
