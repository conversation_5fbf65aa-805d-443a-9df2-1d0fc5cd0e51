import { test, expect } from '@playwright/test';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import * as yaml from 'js-yaml';

test.describe('GitHub Actions Workflow Validation', () => {
  const projectRoot = process.cwd();
  
  const requiredWorkflowFiles = [
    '.github/workflows/deploy-staging.yml',
    '.github/workflows/deploy-production.yml',
    '.github/workflows/ci.yml',
    '.github/workflows/tests.yml',
  ];

  const requiredCompositeActions = [
    '.github/actions/validate-deployment-secrets/action.yml',
    '.github/actions/setup-deployment-ssh/action.yml',
    '.github/actions/deploy-with-backup/action.yml',
  ];

  const stagingSecrets = [
    'STAGING_SSH_PRIVATE_KEY',
    'STAGING_HOST',
    'STAGING_USER',
    'STAGING_PATH',
  ];

  const productionSecrets = [
    'PRODUCTION_SSH_PRIVATE_KEY',
    'PRODUCTION_HOST',
    'PRODUCTION_USER',
    'PRODUCTION_PATH',
  ];

  test('workflow files should exist', async () => {
    for (const file of requiredWorkflowFiles) {
      const filePath = join(projectRoot, file);
      expect(existsSync(filePath), `Required workflow file missing: ${file}`).toBeTruthy();
    }
  });

  test('workflow YAML syntax should be valid', async () => {
    for (const file of requiredWorkflowFiles) {
      const filePath = join(projectRoot, file);
      
      if (!existsSync(filePath)) {
        test.skip(`File does not exist: ${file}`);
        continue;
      }

      const content = readFileSync(filePath, 'utf8');
      
      expect(() => {
        yaml.load(content);
      }, `YAML syntax should be valid for: ${file}`).not.toThrow();
    }
  });

  test('composite actions should exist', async () => {
    for (const action of requiredCompositeActions) {
      const filePath = join(projectRoot, action);
      expect(existsSync(filePath), `Required composite action missing: ${action}`).toBeTruthy();
    }
  });

  test('composite actions YAML syntax should be valid', async () => {
    for (const action of requiredCompositeActions) {
      const filePath = join(projectRoot, action);
      
      if (!existsSync(filePath)) {
        test.skip(`File does not exist: ${action}`);
        continue;
      }

      const content = readFileSync(filePath, 'utf8');
      
      expect(() => {
        yaml.load(content);
      }, `YAML syntax should be valid for: ${action}`).not.toThrow();
    }
  });

  test('staging workflow should have secret validation job', async () => {
    const filePath = join(projectRoot, '.github/workflows/deploy-staging.yml');
    
    if (!existsSync(filePath)) {
      test.skip('Staging workflow file does not exist');
      return;
    }

    const content = readFileSync(filePath, 'utf8');
    expect(content).toContain('validate-secrets:');
  });

  test('production workflow should have secret validation job', async () => {
    const filePath = join(projectRoot, '.github/workflows/deploy-production.yml');
    
    if (!existsSync(filePath)) {
      test.skip('Production workflow file does not exist');
      return;
    }

    const content = readFileSync(filePath, 'utf8');
    expect(content).toContain('validate-secrets:');
  });

  test('staging secrets should be documented', async () => {
    const filePath = join(projectRoot, '.github/workflows/deploy-staging.yml');
    
    if (!existsSync(filePath)) {
      test.skip('Staging workflow file does not exist');
      return;
    }

    const content = readFileSync(filePath, 'utf8');
    
    for (const secret of stagingSecrets) {
      expect(content, `Staging secret not documented: ${secret}`).toContain(secret);
    }
  });

  test('production secrets should be documented', async () => {
    const filePath = join(projectRoot, '.github/workflows/deploy-production.yml');
    
    if (!existsSync(filePath)) {
      test.skip('Production workflow file does not exist');
      return;
    }

    const content = readFileSync(filePath, 'utf8');
    
    for (const secret of productionSecrets) {
      expect(content, `Production secret not documented: ${secret}`).toContain(secret);
    }
  });

  test('workflows should have proper job dependencies', async () => {
    const deploymentWorkflows = [
      '.github/workflows/deploy-staging.yml',
      '.github/workflows/deploy-production.yml',
    ];

    for (const workflow of deploymentWorkflows) {
      const filePath = join(projectRoot, workflow);
      
      if (!existsSync(filePath)) {
        test.skip(`Workflow file does not exist: ${workflow}`);
        continue;
      }

      const content = readFileSync(filePath, 'utf8');
      const needsCount = (content.match(/needs:/g) || []).length;
      
      expect(needsCount, `Insufficient job dependencies in ${workflow}`).toBeGreaterThanOrEqual(3);
    }
  });

  test('workflows should have environment configuration', async () => {
    // Test staging environment
    const stagingPath = join(projectRoot, '.github/workflows/deploy-staging.yml');
    if (existsSync(stagingPath)) {
      const content = readFileSync(stagingPath, 'utf8');
      expect(content).toContain('environment:');
      expect(content).toContain('name: staging');
    }

    // Test production environment
    const productionPath = join(projectRoot, '.github/workflows/deploy-production.yml');
    if (existsSync(productionPath)) {
      const content = readFileSync(productionPath, 'utf8');
      expect(content).toContain('environment:');
      expect(content).toContain('name: production');
    }
  });

  test('validation script should exist and be executable', async () => {
    const scriptPath = join(projectRoot, 'scripts/validate-workflows.sh');
    
    expect(existsSync(scriptPath), 'Validation script missing').toBeTruthy();
    
    // Check if file is executable (on Unix-like systems)
    if (process.platform !== 'win32') {
      const fs = require('fs');
      const stats = fs.statSync(scriptPath);
      const isExecutable = !!(stats.mode & parseInt('111', 8));
      expect(isExecutable, 'Validation script is not executable').toBeTruthy();
    }
  });

  test('workflow structure integrity should be maintained', async () => {
    const deploymentWorkflows = [
      '.github/workflows/deploy-staging.yml',
      '.github/workflows/deploy-production.yml',
    ];

    for (const workflow of deploymentWorkflows) {
      const filePath = join(projectRoot, workflow);
      
      if (!existsSync(filePath)) {
        continue;
      }

      const content = readFileSync(filePath, 'utf8');
      const parsed = yaml.load(content) as any;
      
      // Check basic structure
      expect(parsed).toHaveProperty('name');
      expect(parsed).toHaveProperty('on');
      expect(parsed).toHaveProperty('jobs');
      
      // Check for validate-secrets job
      expect(parsed.jobs).toHaveProperty('validate-secrets');
      
      // Check job dependencies include validate-secrets for deployment jobs
      for (const [jobName, job] of Object.entries(parsed.jobs)) {
        if (jobName !== 'validate-secrets' && (job as any).needs) {
          const needs = Array.isArray((job as any).needs) ? (job as any).needs : [(job as any).needs];
          if (jobName.includes('deploy') || jobName.includes('build')) {
            expect(needs, `Job '${jobName}' should depend on 'validate-secrets' in ${workflow}`)
              .toContain('validate-secrets');
          }
        }
      }
    }
  });

  test('composite action structure should be valid', async () => {
    for (const action of requiredCompositeActions) {
      const filePath = join(projectRoot, action);
      
      if (!existsSync(filePath)) {
        continue;
      }

      const content = readFileSync(filePath, 'utf8');
      const parsed = yaml.load(content) as any;
      
      // Check basic structure
      expect(parsed, `Missing 'name' in ${action}`).toHaveProperty('name');
      expect(parsed, `Missing 'description' in ${action}`).toHaveProperty('description');
      expect(parsed, `Missing 'runs' in ${action}`).toHaveProperty('runs');
      
      // Check runs configuration
      expect(parsed.runs.using, `Action should use 'composite' in ${action}`).toBe('composite');
      expect(parsed.runs, `Missing 'steps' in runs configuration for ${action}`).toHaveProperty('steps');
    }
  });

  test('workflow validation script should run successfully', async () => {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      const { stdout, stderr } = await execAsync('./tests/simple-workflow-test.sh', {
        cwd: projectRoot,
        timeout: 30000
      });
      
      expect(stderr).toBe('');
      expect(stdout).toContain('Simple workflow validation completed');
    } catch (error) {
      console.error('Validation script failed:', error);
      throw error;
    }
  });
});
