name: 'Setup Deployment SSH'
description: 'Sets up SSH connection for deployment with validation and testing'
author: 'EcommFlex Team'
inputs:
  ssh_private_key:
    description: 'SSH private key'
    required: true
  host:
    description: 'Deployment host'
    required: true
  user:
    description: 'SSH user'
    required: true
  environment:
    description: 'Deployment environment (for logging)'
    required: true

outputs:
  ssh-ready:
    description: 'Whether SSH is ready for deployment'
    value: ${{ steps.test-connection.outputs.ssh-ready }}
runs:
  using: 'composite'
  steps:
    - name: Setup SSH configuration
      shell: bash
      env:
        SSH_PRIVATE_KEY: ${{ inputs.ssh_private_key }}
        HOST: ${{ inputs.host }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🔧 Setting up SSH connection for $ENVIRONMENT deployment..."

        # Validate inputs
        if [[ -z "$SSH_PRIVATE_KEY" ]]; then
          echo "❌ SSH private key is not provided"
          exit 1
        fi

        if [[ -z "$HOST" ]]; then
          echo "❌ Host is not provided"
          exit 1
        fi

        # Create SSH directory
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh

        # Setup SSH private key
        echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa

        # Validate SSH key format
        if ! ssh-keygen -l -f ~/.ssh/id_rsa &>/dev/null; then
          echo "❌ Invalid SSH private key format"
          exit 1
        fi

        echo "✅ SSH private key configured successfully"

        # Add host to known_hosts
        echo "🔍 Adding $HOST to known_hosts..."
        ssh-keyscan -H "$HOST" >> ~/.ssh/known_hosts 2>/dev/null || {
          echo "⚠️ Could not add $HOST to known_hosts (host may be unreachable)"
        }

        # Create SSH config for better connection handling
        cat > ~/.ssh/config << EOF
        Host deployment-host
          HostName $HOST
          User ${{ inputs.user }}
          IdentityFile ~/.ssh/id_rsa
          StrictHostKeyChecking no
          UserKnownHostsFile ~/.ssh/known_hosts
          ConnectTimeout 30
          ServerAliveInterval 60
          ServerAliveCountMax 3
        EOF

        chmod 600 ~/.ssh/config
        echo "✅ SSH configuration completed"

    - name: Test SSH connection
      id: test-connection
      shell: bash
      env:
        HOST: ${{ inputs.host }}
        USER: ${{ inputs.user }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🔍 Testing SSH connection to $ENVIRONMENT server..."

        # Test basic connectivity
        if ! timeout 30 nc -z "$HOST" 22 2>/dev/null; then
          echo "❌ Cannot reach SSH port (22) on $HOST"
          echo "ssh-ready=false" >> $GITHUB_OUTPUT
          exit 1
        fi

        echo "✅ SSH port is accessible"

        # Test SSH authentication
        if ssh -o ConnectTimeout=30 -o BatchMode=yes "$USER@$HOST" "echo 'SSH connection successful'" 2>/dev/null; then
          echo "✅ SSH authentication successful"
          echo "ssh-ready=true" >> $GITHUB_OUTPUT
        else
          echo "❌ SSH authentication failed"
          echo "Please verify:"
          echo "  - SSH private key is correct"
          echo "  - SSH user has access to the server"
          echo "  - SSH key is authorized on the server"
          echo "  - Server allows SSH key authentication"
          echo "ssh-ready=false" >> $GITHUB_OUTPUT
          exit 1
        fi

    - name: Test deployment directory access
      shell: bash
      env:
        HOST: ${{ inputs.host }}
        USER: ${{ inputs.user }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🔍 Testing deployment directory access..."

        # Test if we can access the deployment directory
        if ssh "$USER@$HOST" "pwd && ls -la" 2>/dev/null; then
          echo "✅ Deployment directory access confirmed"
        else
          echo "⚠️ Could not verify deployment directory access"
          echo "This may be normal if the directory doesn't exist yet"
        fi

        # Test sudo access (if needed for service restarts)
        if ssh "$USER@$HOST" "sudo -n true" 2>/dev/null; then
          echo "✅ Sudo access confirmed (passwordless)"
        else
          echo "⚠️ No passwordless sudo access detected"
          echo "Service restart commands may require manual intervention"
        fi

        echo "✅ SSH setup and testing completed successfully"

branding:
  icon: 'terminal'
  color: 'blue'
