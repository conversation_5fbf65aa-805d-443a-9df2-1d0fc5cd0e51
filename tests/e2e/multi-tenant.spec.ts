import { test, expect } from '@playwright/test';

test.describe('Multi-Tenant Functionality', () => {
  test('different tenants have isolated data', async ({ browser }) => {
    // Create two browser contexts for different tenants
    const tenant1Context = await browser.newContext({
      baseURL: 'https://tenant1.ecommflex.com'
    });
    const tenant2Context = await browser.newContext({
      baseURL: 'https://tenant2.ecommflex.com'
    });

    const tenant1Page = await tenant1Context.newPage();
    const tenant2Page = await tenant2Context.newPage();

    // Register users on different tenants
    await tenant1Page.goto('/register');
    await tenant1Page.fill('[name="name"]', 'Tenant 1 User');
    await tenant1Page.fill('[name="email"]', '<EMAIL>');
    await tenant1Page.fill('[name="password"]', 'password123');
    await tenant1Page.fill('[name="password_confirmation"]', 'password123');
    await tenant1Page.click('button[type="submit"]');

    await tenant2Page.goto('/register');
    await tenant2Page.fill('[name="name"]', 'Tenant 2 User');
    await tenant2Page.fill('[name="email"]', '<EMAIL>');
    await tenant2Page.fill('[name="password"]', 'password123');
    await tenant2Page.fill('[name="password_confirmation"]', 'password123');
    await tenant2Page.click('button[type="submit"]');

    // Verify both users are logged into their respective tenants
    await expect(tenant1Page.locator('text=Welcome, Tenant 1 User')).toBeVisible();
    await expect(tenant2Page.locator('text=Welcome, Tenant 2 User')).toBeVisible();

    // Verify tenant isolation - try to login to tenant1 with tenant2 credentials
    await tenant1Page.goto('/logout');
    await tenant1Page.goto('/login');
    await tenant1Page.fill('[name="email"]', '<EMAIL>');
    await tenant1Page.fill('[name="password"]', 'password123');
    await tenant1Page.click('button[type="submit"]');

    // Should fail to login
    await expect(tenant1Page.locator('text=Invalid credentials')).toBeVisible();

    await tenant1Context.close();
    await tenant2Context.close();
  });

  test('tenant subdomain routing works correctly', async ({ page }) => {
    // Test different tenant subdomains
    const tenants = ['tenant1', 'tenant2', 'demo'];

    for (const tenant of tenants) {
      await page.goto(`https://${tenant}.ecommflex.com`);
      
      // Verify tenant-specific branding or content
      await expect(page.locator(`[data-tenant="${tenant}"]`)).toBeVisible();
      
      // Verify tenant name in page title or header
      await expect(page).toHaveTitle(new RegExp(tenant, 'i'));
    }
  });

  test('tenant-specific configurations are applied', async ({ page }) => {
    // Test tenant1 with specific theme/configuration
    await page.goto('https://tenant1.ecommflex.com');
    
    // Check for tenant-specific styling or configuration
    const tenant1Theme = await page.locator('body').getAttribute('class');
    expect(tenant1Theme).toContain('tenant1-theme');

    // Test tenant2 with different configuration
    await page.goto('https://tenant2.ecommflex.com');
    
    const tenant2Theme = await page.locator('body').getAttribute('class');
    expect(tenant2Theme).toContain('tenant2-theme');
    expect(tenant2Theme).not.toContain('tenant1-theme');
  });

  test('tenant database isolation is maintained', async ({ page }) => {
    // Login to tenant1
    await page.goto('https://tenant1.ecommflex.com/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    // Create some data (e.g., a product)
    await page.goto('/admin/products/create');
    await page.fill('[name="name"]', 'Tenant 1 Exclusive Product');
    await page.fill('[name="price"]', '99.99');
    await page.click('button[type="submit"]');

    // Verify product was created
    await page.goto('/admin/products');
    await expect(page.locator('text=Tenant 1 Exclusive Product')).toBeVisible();

    // Switch to tenant2 and verify the product is not visible
    await page.goto('https://tenant2.ecommflex.com/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    await page.goto('/admin/products');
    await expect(page.locator('text=Tenant 1 Exclusive Product')).not.toBeVisible();
  });

  test('tenant-specific payment configurations work', async ({ page }) => {
    // Test tenant1 payment configuration
    await page.goto('https://tenant1.ecommflex.com');
    await page.goto('/checkout');

    // Verify tenant1-specific payment methods
    await expect(page.locator('[data-payment="stripe"]')).toBeVisible();
    await expect(page.locator('[data-payment="paypal"]')).toBeVisible();

    // Test tenant2 with different payment configuration
    await page.goto('https://tenant2.ecommflex.com');
    await page.goto('/checkout');

    // Verify tenant2-specific payment methods (different from tenant1)
    await expect(page.locator('[data-payment="razorpay"]')).toBeVisible();
    await expect(page.locator('[data-payment="bank-transfer"]')).toBeVisible();
  });

  test('tenant resource limits are enforced', async ({ page }) => {
    // Login to a tenant with limited resources
    await page.goto('https://limited-tenant.ecommflex.com/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    // Try to exceed user limit
    await page.goto('/admin/users/create');
    
    // Should show limit exceeded message if at limit
    const limitMessage = page.locator('text=User limit exceeded');
    if (await limitMessage.isVisible()) {
      await expect(limitMessage).toBeVisible();
      await expect(page.locator('button[type="submit"]')).toBeDisabled();
    }
  });

  test('tenant custom domains work correctly', async ({ page }) => {
    // Test custom domain for tenant
    await page.goto('https://custom-domain.com');
    
    // Should load the tenant's store
    await expect(page.locator('[data-tenant="custom-tenant"]')).toBeVisible();
    
    // Verify tenant-specific content
    await expect(page.locator('text=Custom Domain Store')).toBeVisible();
  });

  test('tenant backup and restore functionality', async ({ page }) => {
    // Login as super admin
    await page.goto('https://admin.ecommflex.com/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'superadmin123');
    await page.click('button[type="submit"]');

    // Navigate to tenant management
    await page.goto('/super-admin/tenants');
    
    // Find a specific tenant and create backup
    await page.click('[data-tenant-id="tenant1"] .backup-button');
    
    // Verify backup creation
    await expect(page.locator('text=Backup created successfully')).toBeVisible();
    
    // Verify backup appears in backup list
    await page.goto('/super-admin/backups');
    await expect(page.locator('[data-backup-tenant="tenant1"]')).toBeVisible();
  });

  test('tenant analytics and reporting are isolated', async ({ page }) => {
    // Login to tenant1
    await page.goto('https://tenant1.ecommflex.com/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    // Check analytics dashboard
    await page.goto('/admin/analytics');
    
    // Verify tenant1-specific data
    const salesData = await page.locator('[data-metric="total-sales"]').textContent();
    
    // Switch to tenant2 and verify different data
    await page.goto('https://tenant2.ecommflex.com/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    await page.goto('/admin/analytics');
    
    const tenant2SalesData = await page.locator('[data-metric="total-sales"]').textContent();
    
    // Data should be different between tenants
    expect(salesData).not.toBe(tenant2SalesData);
  });

  test('tenant migration and scaling works', async ({ page }) => {
    // Login as super admin
    await page.goto('https://admin.ecommflex.com/login');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'superadmin123');
    await page.click('button[type="submit"]');

    // Navigate to tenant scaling options
    await page.goto('/super-admin/tenants/tenant1/scale');
    
    // Upgrade tenant plan
    await page.click('[data-plan="premium"]');
    await page.click('button[type="submit"]');
    
    // Verify upgrade success
    await expect(page.locator('text=Tenant upgraded successfully')).toBeVisible();
    
    // Verify new features are available for the tenant
    await page.goto('https://tenant1.ecommflex.com');
    await expect(page.locator('[data-feature="premium-analytics"]')).toBeVisible();
  });
});
