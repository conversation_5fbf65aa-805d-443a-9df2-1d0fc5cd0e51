# GitHub Environments Setup Guide

## Overview
This guide explains how to configure GitHub environments for the EcommFlex deployment workflows.

## Required Environments

### 1. Staging Environment
- **Name**: `staging`
- **URL**: `https://staging.ecommflex.com`
- **Protection Rules**: None (automatic deployment)

### 2. Production Environment
- **Name**: `production`
- **URL**: `https://ecommflex.com`
- **Protection Rules**: 
  - Required reviewers (at least 1)
  - Wait timer (optional)

### 3. Approval Environment
- **Name**: `approval`
- **Purpose**: Manual approval gate for production deployments
- **Protection Rules**: Required reviewers

## Setup Instructions

1. Go to repository **Settings** > **Environments**
2. Click **New environment**
3. Enter environment name
4. Configure protection rules as needed
5. Add environment-specific secrets if required

## Environment Variables

Each environment can have its own variables:
- `DATABASE_URL`
- `CACHE_DRIVER`
- `QUEUE_CONNECTION`
- Custom application settings

## Deployment Protection Rules

### Staging
- No protection rules (automatic deployment)
- Deploy on push to `staging` branch

### Production
- Required reviewers: 1-2 team members
- Optional wait timer: 5-10 minutes
- Deploy only from `master` branch
- Manual approval required

## Secrets Configuration

Environment-specific secrets should be configured at the environment level:
- Database credentials
- API keys
- Service tokens
- SSL certificates

## Best Practices

1. **Least Privilege**: Only grant necessary permissions
2. **Review Process**: Always require code review for production
3. **Monitoring**: Set up alerts for deployment failures
4. **Rollback Plan**: Ensure quick rollback capabilities
5. **Documentation**: Keep deployment procedures documented
