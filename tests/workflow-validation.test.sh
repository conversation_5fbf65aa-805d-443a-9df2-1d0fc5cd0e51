#!/bin/bash

# GitHub Actions Workflow Validation Test Suite
# This script provides comprehensive testing for workflow validation functionality

set -e

# Test configuration
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$TEST_DIR")"
TEMP_DIR="/tmp/workflow-test-$$"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Setup and cleanup functions
setup_test_env() {
    mkdir -p "$TEMP_DIR"
    cd "$PROJECT_ROOT"
}

cleanup_test_env() {
    rm -rf "$TEMP_DIR"
}

# Test result functions
print_test_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

print_test_info() {
    echo -e "${BLUE}🧪 $1${NC}"
}

# Test functions
test_workflow_files_exist() {
    print_test_info "Testing workflow file existence"
    
    local required_files=(
        ".github/workflows/deploy-staging.yml"
        ".github/workflows/deploy-production.yml"
        ".github/workflows/ci.yml"
        ".github/workflows/tests.yml"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_test_result 0 "Workflow file exists: $file"
        else
            print_test_result 1 "Missing workflow file: $file"
        fi
    done
}

test_yaml_syntax() {
    print_test_info "Testing YAML syntax validation"
    
    local workflow_files=(.github/workflows/*.yml)
    
    for file in "${workflow_files[@]}"; do
        if [ -f "$file" ]; then
            # Test with Python YAML parser
            if command -v python3 &> /dev/null; then
                if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
                    print_test_result 0 "YAML syntax valid: $file"
                else
                    print_test_result 1 "YAML syntax invalid: $file"
                fi
            else
                print_test_result 1 "Python3 not available for YAML validation"
            fi
        fi
    done
}

test_secret_validation_jobs() {
    print_test_info "Testing secret validation job presence"
    
    local deployment_workflows=(
        ".github/workflows/deploy-staging.yml"
        ".github/workflows/deploy-production.yml"
    )
    
    for workflow in "${deployment_workflows[@]}"; do
        if [ -f "$workflow" ]; then
            if grep -q "validate-secrets:" "$workflow"; then
                print_test_result 0 "Secret validation job found in: $workflow"
            else
                print_test_result 1 "Missing secret validation job in: $workflow"
            fi
        fi
    done
}

test_job_dependencies() {
    print_test_info "Testing job dependencies"
    
    local deployment_workflows=(
        ".github/workflows/deploy-staging.yml"
        ".github/workflows/deploy-production.yml"
    )
    
    for workflow in "${deployment_workflows[@]}"; do
        if [ -f "$workflow" ]; then
            local needs_count=$(grep -c "needs:" "$workflow" || true)
            if [ "$needs_count" -ge 3 ]; then
                print_test_result 0 "Sufficient job dependencies in: $workflow"
            else
                print_test_result 1 "Insufficient job dependencies in: $workflow (found: $needs_count)"
            fi
        fi
    done
}

test_environment_configuration() {
    print_test_info "Testing environment configuration"
    
    # Test staging environment
    if [ -f ".github/workflows/deploy-staging.yml" ]; then
        if grep -q "environment:" .github/workflows/deploy-staging.yml && \
           grep -q "name: staging" .github/workflows/deploy-staging.yml; then
            print_test_result 0 "Staging environment configured"
        else
            print_test_result 1 "Staging environment not properly configured"
        fi
    fi
    
    # Test production environment
    if [ -f ".github/workflows/deploy-production.yml" ]; then
        if grep -q "environment:" .github/workflows/deploy-production.yml && \
           grep -q "name: production" .github/workflows/deploy-production.yml; then
            print_test_result 0 "Production environment configured"
        else
            print_test_result 1 "Production environment not properly configured"
        fi
    fi
}

test_secret_documentation() {
    print_test_info "Testing secret documentation"
    
    local staging_secrets=("STAGING_SSH_PRIVATE_KEY" "STAGING_HOST" "STAGING_USER" "STAGING_PATH")
    local production_secrets=("PRODUCTION_SSH_PRIVATE_KEY" "PRODUCTION_HOST" "PRODUCTION_USER" "PRODUCTION_PATH")
    
    # Test staging secrets documentation
    if [ -f ".github/workflows/deploy-staging.yml" ]; then
        local missing_staging=0
        for secret in "${staging_secrets[@]}"; do
            if ! grep -q "$secret" .github/workflows/deploy-staging.yml; then
                ((missing_staging++))
            fi
        done
        
        if [ "$missing_staging" -eq 0 ]; then
            print_test_result 0 "All staging secrets documented"
        else
            print_test_result 1 "Missing staging secret documentation ($missing_staging secrets)"
        fi
    fi
    
    # Test production secrets documentation
    if [ -f ".github/workflows/deploy-production.yml" ]; then
        local missing_production=0
        for secret in "${production_secrets[@]}"; do
            if ! grep -q "$secret" .github/workflows/deploy-production.yml; then
                ((missing_production++))
            fi
        done
        
        if [ "$missing_production" -eq 0 ]; then
            print_test_result 0 "All production secrets documented"
        else
            print_test_result 1 "Missing production secret documentation ($missing_production secrets)"
        fi
    fi
}

test_composite_actions() {
    print_test_info "Testing composite actions"
    
    local composite_actions=(
        ".github/actions/validate-deployment-secrets/action.yml"
        ".github/actions/setup-deployment-ssh/action.yml"
        ".github/actions/deploy-with-backup/action.yml"
    )
    
    for action in "${composite_actions[@]}"; do
        if [ -f "$action" ]; then
            print_test_result 0 "Composite action exists: $action"
            
            # Test YAML syntax
            if command -v python3 &> /dev/null; then
                if python3 -c "import yaml; yaml.safe_load(open('$action'))" 2>/dev/null; then
                    print_test_result 0 "Composite action YAML valid: $action"
                else
                    print_test_result 1 "Composite action YAML invalid: $action"
                fi
            fi
        else
            print_test_result 1 "Missing composite action: $action"
        fi
    done
}

test_validation_script() {
    print_test_info "Testing validation script"
    
    if [ -f "scripts/validate-workflows.sh" ]; then
        print_test_result 0 "Validation script exists"
        
        # Test script is executable
        if [ -x "scripts/validate-workflows.sh" ]; then
            print_test_result 0 "Validation script is executable"
        else
            print_test_result 1 "Validation script is not executable"
        fi
        
        # Test script syntax
        if bash -n scripts/validate-workflows.sh; then
            print_test_result 0 "Validation script syntax is valid"
        else
            print_test_result 1 "Validation script has syntax errors"
        fi
    else
        print_test_result 1 "Validation script missing"
    fi
}

# Main test execution
main() {
    echo "🚀 Starting GitHub Actions Workflow Test Suite"
    echo "============================================================"
    
    setup_test_env
    
    # Run all tests
    test_workflow_files_exist
    test_yaml_syntax
    test_secret_validation_jobs
    test_job_dependencies
    test_environment_configuration
    test_secret_documentation
    test_composite_actions
    test_validation_script
    
    cleanup_test_env
    
    # Print summary
    echo ""
    echo "============================================================"
    echo "📊 Test Summary:"
    echo "  Tests Passed: $TESTS_PASSED"
    echo "  Tests Failed: $TESTS_FAILED"
    echo "============================================================"
    
    if [ "$TESTS_FAILED" -eq 0 ]; then
        echo -e "${GREEN}✅ All workflow validation tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}❌ $TESTS_FAILED workflow validation tests failed.${NC}"
        exit 1
    fi
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
