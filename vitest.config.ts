import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/frontend/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        'coverage/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/vendor/**',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './resources/js'),
      '@/components': path.resolve(__dirname, './resources/js/components'),
      '@/pages': path.resolve(__dirname, './resources/js/pages'),
      '@/types': path.resolve(__dirname, './resources/js/types'),
      '@/utils': path.resolve(__dirname, './resources/js/utils'),
    },
  },
})
