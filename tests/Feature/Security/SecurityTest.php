<?php

namespace Tests\Feature\Security;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

/**
 * @group security
 */
class SecurityTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @test */
    public function application_prevents_sql_injection_attacks()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Attempt SQL injection in various endpoints
        $maliciousInputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/*",
            "1; DELETE FROM users WHERE 1=1; --",
        ];

        foreach ($maliciousInputs as $input) {
            $response = $this->getJson("/api/users?search=" . urlencode($input));
            
            // Should not cause SQL errors or unauthorized data access
            $this->assertNotEquals(500, $response->getStatusCode());
            
            // Verify users table still exists and has data
            $this->assertDatabaseHas('users', ['id' => $user->id]);
        }
    }

    /** @test */
    public function application_prevents_xss_attacks()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $xssPayloads = [
            '<script>alert("XSS")</script>',
            '<img src="x" onerror="alert(1)">',
            'javascript:alert("XSS")',
            '<svg onload="alert(1)">',
        ];

        foreach ($xssPayloads as $payload) {
            $response = $this->postJson('/api/profile', [
                'name' => $payload,
                'email' => $user->email,
            ]);

            // Should either reject the input or properly escape it
            if ($response->isSuccessful()) {
                $user->refresh();
                // Verify the payload is escaped/sanitized
                $this->assertStringNotContainsString('<script>', $user->name);
                $this->assertStringNotContainsString('javascript:', $user->name);
            }
        }
    }

    /** @test */
    public function application_has_csrf_protection()
    {
        $user = User::factory()->create();

        // Attempt to make a POST request without CSRF token
        $response = $this->postJson('/api/profile', [
            'name' => 'New Name',
            'email' => $user->email,
        ]);

        // Should be rejected due to missing CSRF token
        $this->assertEquals(419, $response->getStatusCode());
    }

    /** @test */
    public function application_enforces_rate_limiting()
    {
        $user = User::factory()->create();

        // Make multiple rapid requests to test rate limiting
        $responses = [];
        for ($i = 0; $i < 100; $i++) {
            $responses[] = $this->postJson('/api/login', [
                'email' => $user->email,
                'password' => 'wrong-password',
            ]);
        }

        // Should eventually hit rate limit
        $rateLimitedResponses = array_filter($responses, function ($response) {
            return $response->getStatusCode() === 429;
        });

        $this->assertGreaterThan(0, count($rateLimitedResponses));
    }

    /** @test */
    public function passwords_are_properly_hashed()
    {
        $password = 'test-password-123';
        
        $user = User::factory()->create([
            'password' => Hash::make($password),
        ]);

        // Verify password is hashed
        $this->assertNotEquals($password, $user->password);
        $this->assertTrue(Hash::check($password, $user->password));

        // Verify password is not stored in plain text in database
        $this->assertDatabaseMissing('users', [
            'id' => $user->id,
            'password' => $password,
        ]);
    }

    /** @test */
    public function sensitive_data_is_not_exposed_in_api_responses()
    {
        $user = User::factory()->create([
            'password' => Hash::make('secret-password'),
        ]);

        $this->actingAs($user);

        $response = $this->getJson('/api/user');

        $response->assertSuccessful();
        
        // Verify sensitive fields are not exposed
        $response->assertJsonMissing(['password']);
        $response->assertJsonMissing(['remember_token']);
        
        // Verify expected fields are present
        $response->assertJsonStructure([
            'id',
            'name',
            'email',
            'created_at',
            'updated_at',
        ]);
    }

    /** @test */
    public function application_validates_file_uploads()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Test malicious file upload attempts
        $maliciousFiles = [
            // PHP file disguised as image
            ['name' => 'malicious.php.jpg', 'content' => '<?php system($_GET["cmd"]); ?>'],
            // Executable file
            ['name' => 'malicious.exe', 'content' => 'MZ...'], // PE header
            // Script file
            ['name' => 'malicious.js', 'content' => 'alert("XSS")'],
        ];

        foreach ($maliciousFiles as $file) {
            $response = $this->postJson('/api/upload', [
                'file' => $file,
            ]);

            // Should reject malicious files
            $this->assertNotEquals(200, $response->getStatusCode());
        }
    }

    /** @test */
    public function application_has_proper_authentication_controls()
    {
        // Test access to protected routes without authentication
        $protectedRoutes = [
            '/api/user',
            '/api/profile',
            '/api/dashboard',
        ];

        foreach ($protectedRoutes as $route) {
            $response = $this->getJson($route);
            $this->assertEquals(401, $response->getStatusCode());
        }
    }

    /** @test */
    public function application_has_proper_authorization_controls()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $this->actingAs($user1);

        // Try to access another user's data
        $response = $this->getJson("/api/users/{$user2->id}");
        
        // Should be forbidden (403) or not found (404)
        $this->assertContains($response->getStatusCode(), [403, 404]);
    }

    /** @test */
    public function application_logs_security_events()
    {
        // Test that security events are properly logged
        $user = User::factory()->create();

        // Failed login attempt
        $this->postJson('/api/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);

        // Check if security event was logged
        $this->assertFileExists(storage_path('logs/laravel.log'));
        
        $logContent = file_get_contents(storage_path('logs/laravel.log'));
        $this->assertStringContainsString('failed login', strtolower($logContent));
    }

    /** @test */
    public function application_handles_session_security()
    {
        $user = User::factory()->create();

        // Login and get session
        $response = $this->postJson('/api/login', [
            'email' => $user->email,
            'password' => 'password',
        ]);

        $response->assertSuccessful();

        // Verify session security headers
        $this->assertTrue($response->headers->has('Set-Cookie'));
        
        $cookieHeader = $response->headers->get('Set-Cookie');
        $this->assertStringContainsString('HttpOnly', $cookieHeader);
        $this->assertStringContainsString('SameSite', $cookieHeader);
    }

    /** @test */
    public function application_prevents_directory_traversal()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $traversalAttempts = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\config\\sam',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
            '....//....//....//etc/passwd',
        ];

        foreach ($traversalAttempts as $attempt) {
            $response = $this->getJson('/api/files/' . urlencode($attempt));
            
            // Should not allow access to system files
            $this->assertNotEquals(200, $response->getStatusCode());
        }
    }

    /** @test */
    public function application_has_secure_headers()
    {
        $response = $this->get('/');

        // Check for security headers
        $this->assertTrue($response->headers->has('X-Frame-Options'));
        $this->assertTrue($response->headers->has('X-Content-Type-Options'));
        $this->assertTrue($response->headers->has('X-XSS-Protection'));
        
        // Verify header values
        $this->assertEquals('DENY', $response->headers->get('X-Frame-Options'));
        $this->assertEquals('nosniff', $response->headers->get('X-Content-Type-Options'));
        $this->assertEquals('1; mode=block', $response->headers->get('X-XSS-Protection'));
    }
}
