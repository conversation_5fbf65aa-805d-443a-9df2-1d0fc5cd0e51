#!/bin/bash

# GitHub Actions Workflow Validation Script
# This script validates GitHub Actions workflows for common issues and best practices

set -e

echo "🚀 Starting GitHub Actions workflow validation..."
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
WARNINGS=0

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# Function to print warnings
print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
    ((WARNINGS++))
}

# Function to print info
print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Test 1: Check if workflow files exist
echo "🧪 Testing workflow file existence..."
WORKFLOW_FILES=(
    ".github/workflows/deploy-staging.yml"
    ".github/workflows/deploy-production.yml"
    ".github/workflows/ci.yml"
    ".github/workflows/tests.yml"
)

for file in "${WORKFLOW_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_result 0 "Workflow file exists: $file"
    else
        print_result 1 "Missing workflow file: $file"
    fi
done

# Test 2: YAML Syntax Validation
echo "🧪 Testing workflow YAML syntax..."
for file in "${WORKFLOW_FILES[@]}"; do
    if [ -f "$file" ]; then
        if command -v yq &> /dev/null; then
            if yq eval "$file" > /dev/null 2>&1; then
                print_result 0 "YAML syntax valid: $file"
            else
                print_result 1 "YAML syntax invalid: $file"
            fi
        elif command -v python3 &> /dev/null; then
            if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
                print_result 0 "YAML syntax valid: $file"
            else
                print_result 1 "YAML syntax invalid: $file"
            fi
        else
            print_warning "No YAML validator found (yq or python3), skipping syntax test for $file"
        fi
    fi
done

# Test 3: Secret Documentation
echo "🧪 Testing secret documentation..."
STAGING_SECRETS=("STAGING_SSH_PRIVATE_KEY" "STAGING_HOST" "STAGING_USER" "STAGING_PATH")
PRODUCTION_SECRETS=("PRODUCTION_SSH_PRIVATE_KEY" "PRODUCTION_HOST" "PRODUCTION_USER" "PRODUCTION_PATH")

if [ -f ".github/workflows/deploy-staging.yml" ]; then
    MISSING_STAGING_DOCS=0
    for secret in "${STAGING_SECRETS[@]}"; do
        if ! grep -q "$secret" .github/workflows/deploy-staging.yml; then
            print_warning "Missing staging secret documentation: $secret"
            ((MISSING_STAGING_DOCS++))
        fi
    done
    
    if [ "$MISSING_STAGING_DOCS" -eq 0 ]; then
        print_result 0 "All staging secrets are documented"
    else
        print_result 1 "Missing staging secret documentation"
    fi
fi

if [ -f ".github/workflows/deploy-production.yml" ]; then
    MISSING_PRODUCTION_DOCS=0
    for secret in "${PRODUCTION_SECRETS[@]}"; do
        if ! grep -q "$secret" .github/workflows/deploy-production.yml; then
            print_warning "Missing production secret documentation: $secret"
            ((MISSING_PRODUCTION_DOCS++))
        fi
    done
    
    if [ "$MISSING_PRODUCTION_DOCS" -eq 0 ]; then
        print_result 0 "All production secrets are documented"
    else
        print_result 1 "Missing production secret documentation"
    fi
fi

# Test 4: Job Dependencies
echo "🧪 Testing job dependencies..."
for file in .github/workflows/deploy-*.yml; do
    if [ -f "$file" ]; then
        NEEDS_COUNT=$(grep -c "needs:" "$file" || true)
        if [ "$NEEDS_COUNT" -ge 3 ]; then
            print_result 0 "Job dependencies configured: $file"
        else
            print_result 1 "Insufficient job dependencies: $file"
        fi
    fi
done

# Test 5: Environment Configuration
echo "🧪 Testing environment configuration..."
if [ -f ".github/workflows/deploy-staging.yml" ]; then
    if grep -q "environment:" .github/workflows/deploy-staging.yml && grep -q "name: staging" .github/workflows/deploy-staging.yml; then
        print_result 0 "Staging environment configured"
    else
        print_result 1 "Staging environment not properly configured"
    fi
fi

if [ -f ".github/workflows/deploy-production.yml" ]; then
    if grep -q "environment:" .github/workflows/deploy-production.yml && grep -q "name: production" .github/workflows/deploy-production.yml; then
        print_result 0 "Production environment configured"
    else
        print_result 1 "Production environment not properly configured"
    fi
fi

# Test 6: Secret Validation Jobs
echo "🧪 Testing secret validation jobs..."
for file in .github/workflows/deploy-*.yml; do
    if [ -f "$file" ]; then
        if grep -q "validate-secrets:" "$file"; then
            print_result 0 "Secret validation job found: $file"
        else
            print_result 1 "Missing secret validation job: $file"
        fi
    fi
done

# Test 7: Composite Actions
echo "🧪 Testing composite actions..."
COMPOSITE_ACTIONS=(
    ".github/actions/validate-deployment-secrets/action.yml"
    ".github/actions/setup-deployment-ssh/action.yml"
    ".github/actions/deploy-with-backup/action.yml"
)

for action in "${COMPOSITE_ACTIONS[@]}"; do
    if [ -f "$action" ]; then
        print_result 0 "Composite action exists: $action"
    else
        print_result 1 "Missing composite action: $action"
    fi
done

# Summary
echo ""
echo "============================================================"
echo "📊 Validation Summary:"
echo "  Tests Passed: $TESTS_PASSED"
echo "  Tests Failed: $TESTS_FAILED"
echo "  Warnings: $WARNINGS"
echo "============================================================"

if [ "$TESTS_FAILED" -eq 0 ]; then
    echo -e "${GREEN}✅ All workflow validation tests passed!${NC}"
    if [ "$WARNINGS" -gt 0 ]; then
        echo -e "${YELLOW}⚠️ There are $WARNINGS warnings to review.${NC}"
    fi
    exit 0
else
    echo -e "${RED}❌ $TESTS_FAILED workflow validation tests failed.${NC}"
    echo -e "${YELLOW}Please review and fix the issues above.${NC}"
    exit 1
fi
