#!/bin/bash

# GitHub Actions Staging Workflow Validation Test Suite
# This script validates the staging deployment workflow for common issues

# Note: Removed set -e to allow tests to continue even if individual tests fail

echo "🚀 Starting staging deployment workflow validation tests..."
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# Test 1: YAML Syntax Validation
echo "🧪 Testing staging workflow YAML syntax..."
if command -v yq &> /dev/null; then
    yq eval '.github/workflows/deploy-staging.yml' > /dev/null 2>&1
    print_result $? "Staging workflow YAML syntax is valid"
elif command -v python3 &> /dev/null; then
    python3 -c "import yaml; yaml.safe_load(open('.github/workflows/deploy-staging.yml'))" 2>/dev/null
    print_result $? "Staging workflow YAML syntax is valid"
else
    echo -e "${YELLOW}⚠️ No YAML validator found (yq or python3), skipping syntax test${NC}"
fi

# Test 2: Environment Name Validation
echo "🧪 Testing environment names..."
STAGING_ENV_COUNT=$(grep -c "name: staging" .github/workflows/deploy-staging.yml || true)
if [ "$STAGING_ENV_COUNT" -ge 1 ]; then
    print_result 0 "Environment names are valid"
else
    print_result 1 "Environment name validation failed"
fi

# Test 3: Output Reference Consistency
echo "🧪 Testing output references..."
DEPLOYMENT_ID_OUTPUT=$(grep -c "deployment_id:" .github/workflows/deploy-staging.yml || true)
DEPLOYMENT_ID_REFS=$(grep -c "deployment_id" .github/workflows/deploy-staging.yml || true)
DEPLOYMENT_DASH_REFS=$(grep -c "deployment-id" .github/workflows/deploy-staging.yml || true)

if [ "$DEPLOYMENT_DASH_REFS" -eq 0 ] && [ "$DEPLOYMENT_ID_OUTPUT" -ge 1 ] && [ "$DEPLOYMENT_ID_REFS" -ge 3 ]; then
    print_result 0 "Output references are consistent"
else
    print_result 1 "Output reference consistency check failed"
    echo "  deployment_id outputs: $DEPLOYMENT_ID_OUTPUT"
    echo "  deployment_id references: $DEPLOYMENT_ID_REFS"
    echo "  deployment-id references: $DEPLOYMENT_DASH_REFS"
fi

# Test 4: Job Dependencies
echo "🧪 Testing job dependencies..."
NEEDS_COUNT=$(grep -c "needs:" .github/workflows/deploy-staging.yml || true)
if [ "$NEEDS_COUNT" -ge 3 ]; then
    print_result 0 "Job dependencies are correct"
else
    print_result 1 "Job dependencies validation failed"
fi

# Test 5: Secret Documentation
echo "🧪 Testing secret documentation..."
SECRET_COMMENTS=$(grep -c "# Note:.*secret.*must be configured" .github/workflows/deploy-staging.yml || true)
HEADER_DOCS=$(grep -c "# Required Repository Secrets:" .github/workflows/deploy-staging.yml || true)

if [ "$SECRET_COMMENTS" -ge 3 ] && [ "$HEADER_DOCS" -eq 1 ]; then
    print_result 0 "Secrets are properly documented"
else
    print_result 1 "Secret documentation validation failed"
    echo "  Secret comments found: $SECRET_COMMENTS"
    echo "  Header documentation found: $HEADER_DOCS"
fi

# Test 6: Required Secrets Coverage
echo "🧪 Testing required secrets coverage..."
REQUIRED_SECRETS=("STAGING_SSH_PRIVATE_KEY" "STAGING_HOST" "STAGING_USER" "STAGING_PATH")
MISSING_SECRETS=0

for secret in "${REQUIRED_SECRETS[@]}"; do
    if ! grep -q "$secret" .github/workflows/deploy-staging.yml; then
        echo "  Missing secret: $secret"
        ((MISSING_SECRETS++))
    fi
done

if [ "$MISSING_SECRETS" -eq 0 ]; then
    print_result 0 "All required secrets are referenced"
else
    print_result 1 "Missing required secrets"
fi

# Test 7: Environment Configuration
echo "🧪 Testing environment configuration..."
STAGING_ENV_BLOCK=$(grep -A 2 "environment:" .github/workflows/deploy-staging.yml | grep -c "name: staging" || true)
STAGING_URL_BLOCK=$(grep -A 2 "environment:" .github/workflows/deploy-staging.yml | grep -c "url:" || true)

if [ "$STAGING_ENV_BLOCK" -eq 1 ] && [ "$STAGING_URL_BLOCK" -eq 1 ]; then
    print_result 0 "Environment configuration is correct"
else
    print_result 1 "Environment configuration validation failed"
fi

# Test 8: Workflow Triggers
echo "🧪 Testing workflow triggers..."
PUSH_TRIGGER=$(grep -A 5 "on:" .github/workflows/deploy-staging.yml | grep -c "\- staging" || true)
WORKFLOW_DISPATCH=$(grep -c "workflow_dispatch:" .github/workflows/deploy-staging.yml || true)

if [ "$PUSH_TRIGGER" -eq 1 ] && [ "$WORKFLOW_DISPATCH" -eq 1 ]; then
    print_result 0 "Workflow triggers are configured correctly"
else
    print_result 1 "Workflow trigger validation failed"
    echo "  Push trigger count: $PUSH_TRIGGER"
    echo "  Workflow dispatch count: $WORKFLOW_DISPATCH"
fi

# Summary
echo ""
echo "============================================================"
echo "📊 Test Summary:"
echo "  Tests Passed: $TESTS_PASSED"
echo "  Tests Failed: $TESTS_FAILED"
echo "============================================================"

if [ "$TESTS_FAILED" -eq 0 ]; then
    echo -e "${GREEN}✅ All tests passed! The staging workflow fixes are working correctly.${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please review the staging workflow configuration.${NC}"
    exit 1
fi
